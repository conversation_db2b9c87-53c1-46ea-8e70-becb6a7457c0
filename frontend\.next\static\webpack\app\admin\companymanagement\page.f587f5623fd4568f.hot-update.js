"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/companymanagement/page",{

/***/ "(app-pages-browser)/./src/app/admin/companymanagement/page.jsx":
/*!**************************************************!*\
  !*** ./src/app/admin/companymanagement/page.jsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Employers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Edit,Filter,GraduationCap,Heart,Loader,MapPin,PlusCircle,Search,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Edit,Filter,GraduationCap,Heart,Loader,MapPin,PlusCircle,Search,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Edit,Filter,GraduationCap,Heart,Loader,MapPin,PlusCircle,Search,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Edit,Filter,GraduationCap,Heart,Loader,MapPin,PlusCircle,Search,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Edit,Filter,GraduationCap,Heart,Loader,MapPin,PlusCircle,Search,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Edit,Filter,GraduationCap,Heart,Loader,MapPin,PlusCircle,Search,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Edit,Filter,GraduationCap,Heart,Loader,MapPin,PlusCircle,Search,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Edit,Filter,GraduationCap,Heart,Loader,MapPin,PlusCircle,Search,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Edit,Filter,GraduationCap,Heart,Loader,MapPin,PlusCircle,Search,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Edit,Filter,GraduationCap,Heart,Loader,MapPin,PlusCircle,Search,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Edit,Filter,GraduationCap,Heart,Loader,MapPin,PlusCircle,Search,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _api_optimized__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../api/optimized */ \"(app-pages-browser)/./src/api/optimized.js\");\n/* harmony import */ var _api_companies__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../api/companies */ \"(app-pages-browser)/./src/api/companies.js\");\n/* harmony import */ var _utils_auth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../utils/auth */ \"(app-pages-browser)/./src/utils/auth.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n// Import API services\n\n\n\nfunction Employers() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [companies, setCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [tierFilter, setTierFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('ALL');\n    const [industryFilter, setIndustryFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('ALL');\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('name');\n    const [followedCompanies, setFollowedCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        total: 0,\n        tier1: 0,\n        tier2: 0,\n        tier3: 0,\n        campus_recruiting: 0\n    });\n    // Pagination state\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalCount, setTotalCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(8); // Show 8 cards per page\n    // Fetch companies with server-side pagination and filtering\n    const fetchCompanies = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1;\n        try {\n            setLoading(true);\n            setError(null);\n            // Build filter parameters for server-side filtering\n            const params = {\n                page,\n                page_size: pageSize\n            };\n            // Add search filter\n            if (searchTerm) {\n                params.search = searchTerm;\n            }\n            // Add tier filter\n            if (tierFilter !== 'ALL') {\n                params.tier = tierFilter;\n            }\n            // Add industry filter\n            if (industryFilter !== 'ALL') {\n                params.industry = industryFilter;\n            }\n            // Add sorting\n            if (sortBy) {\n                params.ordering = sortBy === 'name' ? 'name' : sortBy === 'jobs' ? '-total_active_jobs' : sortBy === 'applicants' ? '-total_applicants' : sortBy === 'tier' ? 'tier' : 'name';\n            }\n            // Fetch data from optimized API\n            const response = await _api_optimized__WEBPACK_IMPORTED_MODULE_3__.companiesAPI.getCompanies(params);\n            setCompanies(response.data);\n            setCurrentPage(page);\n            setTotalPages(response.pagination.total_pages);\n            setTotalCount(response.pagination.total_count);\n            setLoading(false);\n        } catch (err) {\n            console.error('Error fetching companies:', err);\n            setError('Failed to load companies. Please try again.');\n            setCompanies([]);\n            setLoading(false);\n        }\n    };\n    // Fetch company statistics\n    const fetchStats = async ()=>{\n        try {\n            const statsResponse = await _api_optimized__WEBPACK_IMPORTED_MODULE_3__.companiesAPI.getCompanyStats();\n            setStats(statsResponse.data || statsResponse);\n        } catch (statsError) {\n            console.error('Error fetching company stats:', statsError);\n            // Fallback stats will be calculated from current page data\n            setStats({\n                total: totalCount,\n                tier1: 0,\n                tier2: 0,\n                tier3: 0,\n                campus_recruiting: 0\n            });\n        }\n    };\n    // Initial data fetch\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Employers.useEffect\": ()=>{\n            fetchCompanies();\n            fetchStats();\n            // Load followed companies from API\n            const userId = (0,_utils_auth__WEBPACK_IMPORTED_MODULE_5__.getUserId)();\n            if (userId) {\n                loadFollowedCompanies(userId);\n            }\n        }\n    }[\"Employers.useEffect\"], []);\n    // Function to load followed companies from API\n    const loadFollowedCompanies = async (userId)=>{\n        try {\n            const response = await (0,_api_companies__WEBPACK_IMPORTED_MODULE_4__.getUserFollowedCompanies)(userId);\n            const followedIds = response.data.map((company)=>company.id);\n            setFollowedCompanies(new Set(followedIds));\n        } catch (error) {\n            console.error('Error loading followed companies:', error);\n        }\n    };\n    // Industries will be fetched from backend or hardcoded for now\n    const industries = [\n        'Technology',\n        'Finance',\n        'Healthcare',\n        'Manufacturing',\n        'Consulting',\n        'E-commerce'\n    ];\n    // Companies are already filtered and sorted on the server side\n    const currentCompanies = companies;\n    // Change page\n    const paginate = (pageNumber)=>setCurrentPage(pageNumber);\n    const nextPage = ()=>setCurrentPage((prev)=>Math.min(prev + 1, totalPages));\n    const prevPage = ()=>setCurrentPage((prev)=>Math.max(prev - 1, 1));\n    // Refetch data when filters change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Employers.useEffect\": ()=>{\n            fetchCompanies(1); // Reset to page 1 when filters change\n        }\n    }[\"Employers.useEffect\"], [\n        searchTerm,\n        tierFilter,\n        industryFilter,\n        sortBy,\n        pageSize\n    ]);\n    const toggleFollowCompany = async (e, companyId)=>{\n        e.stopPropagation(); // Prevent navigation when clicking follow button\n        try {\n            const userId = (0,_utils_auth__WEBPACK_IMPORTED_MODULE_5__.getUserId)();\n            if (!userId) {\n                alert(\"Please log in to follow companies\");\n                return;\n            }\n            const newFollowedCompanies = new Set(followedCompanies);\n            if (newFollowedCompanies.has(companyId)) {\n                await unfollowCompany(companyId, userId);\n                newFollowedCompanies.delete(companyId);\n            } else {\n                await followCompany(companyId, userId);\n                newFollowedCompanies.add(companyId);\n            }\n            setFollowedCompanies(newFollowedCompanies);\n        } catch (error) {\n            console.error('Error updating follow status:', error);\n            alert('Failed to update follow status. Please try again.');\n        }\n    };\n    const getTierColor = (tier)=>{\n        switch(tier){\n            case 'Tier 1':\n                return 'bg-emerald-100 text-emerald-800 border-emerald-200';\n            case 'Tier 2':\n                return 'bg-blue-100 text-blue-800 border-blue-200';\n            case 'Tier 3':\n                return 'bg-purple-100 text-purple-800 border-purple-200';\n            default:\n                return 'bg-gray-100 text-gray-800 border-gray-200';\n        }\n    };\n    const handleCompanyClick = (companyId)=>{\n        router.push(\"/admin/companymanagement/\".concat(companyId));\n    };\n    const handleCreateCompany = ()=>{\n        router.push('/admin/companymanagement/create');\n    };\n    const handleEditCompany = (e, companyId)=>{\n        e.stopPropagation(); // Prevent navigation to company detail\n        router.push(\"/admin/companymanagement/edit/\".concat(companyId));\n    };\n    const handleDeleteCompany = async (e, companyId)=>{\n        e.stopPropagation(); // Prevent navigation to company detail\n        if (confirm('Are you sure you want to delete this company? This action cannot be undone.')) {\n            try {\n                await (0,_api_companies__WEBPACK_IMPORTED_MODULE_4__.deleteCompany)(companyId);\n                // Refresh the current page after deletion\n                fetchCompanies(currentPage);\n                alert('Company deleted successfully');\n            } catch (error) {\n                console.error('Error deleting company:', error);\n                alert('Failed to delete company. Please try again.');\n            }\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full flex flex-col items-center justify-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative w-16 h-16 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 border-t-4 border-blue-500 rounded-full animate-spin\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                            lineNumber: 236,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-2 border-r-4 border-transparent rounded-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                            lineNumber: 237,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                    lineNumber: 235,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 mb-6\",\n                    children: \"Loading companies...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                    lineNumber: 239,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n            lineNumber: 234,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center max-w-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-500 mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-16 h-16 mx-auto\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                            lineNumber: 249,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                        lineNumber: 248,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-semibold text-gray-900 mb-2\",\n                        children: \"Error Loading Companies\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                        lineNumber: 251,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-4\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                        lineNumber: 252,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>window.location.reload(),\n                        className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                        lineNumber: 253,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                lineNumber: 247,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n            lineNumber: 246,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full overflow-y-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6 flex justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                    children: \"Company Management\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleCreateCompany,\n                                    className: \"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors shadow-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Create New Company\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                            lineNumber: 269,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-5 gap-4 mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg p-4 shadow-sm border border-gray-100\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-blue-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-5 h-5 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: stats.total\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Total Companies\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg p-4 shadow-sm border border-gray-100\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-emerald-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-5 h-5 text-emerald-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: stats.tier1\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Tier 1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg p-4 shadow-sm border border-gray-100\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-blue-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-5 h-5 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: stats.tier2\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 312,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Tier 2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg p-4 shadow-sm border border-gray-100\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-amber-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-5 h-5 text-amber-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: stats.tier3\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Tier 3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg p-4 shadow-sm border border-gray-100\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-amber-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-5 h-5 text-amber-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: followedCompanies.size\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Following\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                            lineNumber: 281,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg p-6 shadow-sm border border-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col md:flex-row gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"Search companies, industries...\",\n                                                    value: searchTerm,\n                                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                                    className: \"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 349,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: tierFilter,\n                                                    onChange: (e)=>setTierFilter(e.target.value),\n                                                    className: \"px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[120px]\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"ALL\",\n                                                            children: \"All Tiers\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                            lineNumber: 365,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"Tier 1\",\n                                                            children: \"Tier 1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                            lineNumber: 366,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"Tier 2\",\n                                                            children: \"Tier 2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"Tier 3\",\n                                                            children: \"Tier 3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                            lineNumber: 368,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: industryFilter,\n                                                    onChange: (e)=>setIndustryFilter(e.target.value),\n                                                    className: \"px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[150px]\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"ALL\",\n                                                            children: \"All Industries\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        industries.map((industry)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: industry,\n                                                                children: industry\n                                                            }, industry, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                                lineNumber: 378,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: sortBy,\n                                                    onChange: (e)=>setSortBy(e.target.value),\n                                                    className: \"px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[130px]\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"name\",\n                                                            children: \"Company A-Z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                            lineNumber: 387,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"jobs\",\n                                                            children: \"Most Jobs\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                            lineNumber: 388,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"applicants\",\n                                                            children: \"Most Popular\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                            lineNumber: 389,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"tier\",\n                                                            children: \"Tier\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                            lineNumber: 390,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 text-sm text-gray-600\",\n                                    children: [\n                                        \"Showing \",\n                                        filteredCompanies.length,\n                                        \" of \",\n                                        allCompanies.length,\n                                        \" companies\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                            lineNumber: 344,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                    lineNumber: 268,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                    children: currentCompanies.length > 0 ? currentCompanies.map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            onClick: ()=>handleCompanyClick(company.id),\n                            className: \"bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-lg transition-all duration-200 cursor-pointer group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-bold text-lg\",\n                                                        children: company.name.charAt(0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 414,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 413,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-gray-900 group-hover:text-blue-600 transition-colors\",\n                                                            children: company.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                            lineNumber: 419,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: company.industry\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                            lineNumber: 422,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 418,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>handleEditCompany(e, company.id),\n                                                    className: \"p-2 rounded-lg transition-colors bg-blue-100 text-blue-600 hover:bg-blue-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 430,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 426,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>handleDeleteCompany(e, company.id),\n                                                    className: \"p-2 rounded-lg transition-colors bg-red-100 text-red-600 hover:bg-red-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 436,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 432,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 411,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 text-sm text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 444,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: company.location\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 445,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 text-sm text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 448,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: company.size\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 447,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 text-sm text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 452,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: company.founded ? \"Founded \".concat(company.founded) : 'Founded year not specified'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 451,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 442,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 py-1 rounded-full text-xs font-medium border \".concat(getTierColor(company.tier)),\n                                            children: company.tier\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 461,\n                                            columnNumber: 19\n                                        }, this),\n                                        company.campus_recruiting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-700 border border-green-200\",\n                                            children: \"Campus Recruiting\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 465,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 460,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 mb-4 line-clamp-3\",\n                                    children: company.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 472,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-3 gap-4 pt-4 border-t border-gray-100\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg font-bold text-gray-900\",\n                                                    children: company.totalActiveJobs\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: \"Active Jobs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 480,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg font-bold text-gray-900\",\n                                                    children: company.totalApplicants\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 483,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: \"Applicants\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 484,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 482,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg font-bold text-gray-900\",\n                                                    children: company.totalHired\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 487,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: \"Hired\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 488,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 486,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 477,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, company.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                            lineNumber: 405,\n                            columnNumber: 15\n                        }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-full flex flex-col items-center justify-center py-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-12 h-12 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 496,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                lineNumber: 495,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-2\",\n                                children: \"No companies found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                lineNumber: 498,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-4\",\n                                children: \"Try adjusting your search or filters\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                lineNumber: 499,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setSearchTerm('');\n                                    setTierFilter('ALL');\n                                    setIndustryFilter('ALL');\n                                    setSortBy('name');\n                                },\n                                className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                children: \"Clear Filters\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                lineNumber: 500,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                        lineNumber: 494,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                    lineNumber: 402,\n                    columnNumber: 9\n                }, this),\n                filteredCompanies.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 flex justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex items-center space-x-2\",\n                        \"aria-label\": \"Pagination\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: prevPage,\n                                disabled: currentPage === 1,\n                                className: \"px-4 py-2 rounded-md border \".concat(currentPage === 1 ? 'text-gray-400 bg-gray-100 cursor-not-allowed' : 'text-gray-700 hover:bg-gray-100'),\n                                children: \"Previous\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                lineNumber: 519,\n                                columnNumber: 15\n                            }, this),\n                            [\n                                ...Array(Math.min(5, totalPages))\n                            ].map((_, i)=>{\n                                // Logic to display pages around current page\n                                let pageNum;\n                                if (totalPages <= 5) {\n                                    pageNum = i + 1;\n                                } else if (currentPage <= 3) {\n                                    pageNum = i + 1;\n                                } else if (currentPage >= totalPages - 2) {\n                                    pageNum = totalPages - 4 + i;\n                                } else {\n                                    pageNum = currentPage - 2 + i;\n                                }\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>paginate(pageNum),\n                                    className: \"w-10 h-10 flex items-center justify-center rounded-md \".concat(currentPage === pageNum ? 'bg-blue-500 text-white' : 'text-gray-700 hover:bg-gray-100'),\n                                    \"aria-current\": currentPage === pageNum ? 'page' : undefined,\n                                    children: pageNum\n                                }, pageNum, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 544,\n                                    columnNumber: 19\n                                }, this);\n                            }),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: nextPage,\n                                disabled: currentPage === totalPages,\n                                className: \"px-4 py-2 rounded-md border \".concat(currentPage === totalPages ? 'text-gray-400 bg-gray-100 cursor-not-allowed' : 'text-gray-700 hover:bg-gray-100'),\n                                children: \"Next\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                lineNumber: 559,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                        lineNumber: 518,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                    lineNumber: 517,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n            lineNumber: 266,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n        lineNumber: 265,\n        columnNumber: 5\n    }, this);\n}\n_s(Employers, \"slan7mGoUv2bTmoFkjvdsGI+mQU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = Employers;\nvar _c;\n$RefreshReg$(_c, \"Employers\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/companymanagement/page.jsx\n"));

/***/ })

});