"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/student-management/page",{

/***/ "(app-pages-browser)/./src/app/admin/student-management/page.jsx":
/*!***************************************************!*\
  !*** ./src/app/admin/student-management/page.jsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StudentManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_GraduationCap_RefreshCw_Save_Search_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,GraduationCap,RefreshCw,Save,Search,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_GraduationCap_RefreshCw_Save_Search_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,GraduationCap,RefreshCw,Save,Search,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _api_optimized__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../api/optimized */ \"(app-pages-browser)/./src/api/optimized.js\");\n/* harmony import */ var _utils_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../utils/auth */ \"(app-pages-browser)/./src/utils/auth.js\");\n/* harmony import */ var _StudentDropdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./StudentDropdown */ \"(app-pages-browser)/./src/app/admin/student-management/StudentDropdown.jsx\");\n/* harmony import */ var _StudentProfile__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./StudentProfile */ \"(app-pages-browser)/./src/app/admin/student-management/StudentProfile.jsx\");\n/* harmony import */ var _DepartmentCards__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./DepartmentCards */ \"(app-pages-browser)/./src/app/admin/student-management/DepartmentCards.jsx\");\n/* harmony import */ var _PassoutYearCards__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./PassoutYearCards */ \"(app-pages-browser)/./src/app/admin/student-management/PassoutYearCards.jsx\");\n/* harmony import */ var _StudentList__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./StudentList */ \"(app-pages-browser)/./src/app/admin/student-management/StudentList.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction StudentManagement() {\n    var _departmentOptions_find, _departmentOptions_find1;\n    _s();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [debouncedSearchTerm, setDebouncedSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedDepartment, setSelectedDepartment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedYear, setSelectedYear] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [selectedStudent, setSelectedStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editedStudent, setEditedStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isYearDropdownOpen, setIsYearDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isRetrying, setIsRetrying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalStudents, setTotalStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(10);\n    const [availableYears, setAvailableYears] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [departmentStats, setDepartmentStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedPassoutYear, setSelectedPassoutYear] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [cgpaMin, setCgpaMin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [cgpaMax, setCgpaMax] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Dropdown options\n    const departmentOptions = [\n        {\n            value: 'Computer Science',\n            label: 'Computer Science'\n        },\n        {\n            value: 'Electronics',\n            label: 'Electronics'\n        },\n        {\n            value: 'Mechanical',\n            label: 'Mechanical'\n        },\n        {\n            value: 'Civil',\n            label: 'Civil'\n        },\n        {\n            value: 'Electrical',\n            label: 'Electrical'\n        },\n        {\n            value: 'Information Technology',\n            label: 'Information Technology'\n        },\n        {\n            value: 'Chemical',\n            label: 'Chemical'\n        },\n        {\n            value: 'Biotechnology',\n            label: 'Biotechnology'\n        }\n    ];\n    // Transform student data from API response\n    const transformStudentData = (student)=>({\n            id: student.id,\n            rollNumber: student.student_id || 'N/A',\n            name: \"\".concat(student.first_name || '', \" \").concat(student.last_name || '').trim() || 'Unknown',\n            email: student.contact_email || student.email || 'N/A',\n            phone: student.phone || 'N/A',\n            department: student.branch || 'N/A',\n            year: getYearFromBranch(student.branch, student),\n            cgpa: student.gpa || 'N/A',\n            gpa: student.gpa || 'N/A',\n            address: student.address || 'N/A',\n            dateOfBirth: student.date_of_birth || '',\n            parentContact: student.parent_contact || 'N/A',\n            education: student.education || 'N/A',\n            skills: student.skills || [],\n            // Academic details\n            joining_year: student.joining_year || student.admission_year || '',\n            passout_year: student.passout_year || student.graduation_year || '',\n            // Class XII details\n            twelfth_cgpa: student.twelfth_cgpa || student.class_12_cgpa || '',\n            twelfth_percentage: student.twelfth_percentage || student.class_12_percentage || '',\n            twelfth_year_of_passing: student.twelfth_year_of_passing || student.class_12_year || '',\n            twelfth_school: student.twelfth_school || student.class_12_school || '',\n            twelfth_board: student.twelfth_board || student.class_12_board || '',\n            twelfth_location: student.twelfth_location || student.class_12_location || '',\n            twelfth_specialization: student.twelfth_specialization || student.class_12_stream || '',\n            // Class X details\n            tenth_cgpa: student.tenth_cgpa || student.class_10_cgpa || '',\n            tenth_percentage: student.tenth_percentage || student.class_10_percentage || '',\n            tenth_year_of_passing: student.tenth_year_of_passing || student.class_10_year || '',\n            tenth_school: student.tenth_school || student.class_10_school || '',\n            tenth_board: student.tenth_board || student.class_10_board || '',\n            tenth_location: student.tenth_location || student.class_10_location || '',\n            tenth_specialization: student.tenth_specialization || student.class_10_stream || '',\n            // Address details\n            city: student.city || '',\n            district: student.district || '',\n            state: student.state || '',\n            pincode: student.pincode || student.pin_code || '',\n            country: student.country || 'India',\n            // Certificate URLs\n            tenth_certificate: student.tenth_certificate || student.class_10_certificate || '',\n            twelfth_certificate: student.twelfth_certificate || student.class_12_certificate || '',\n            tenth_certificate_url: student.tenth_certificate_url || student.class_10_certificate_url || '',\n            twelfth_certificate_url: student.twelfth_certificate_url || student.class_12_certificate_url || '',\n            // Resume details\n            resume: student.resume || '',\n            resume_url: student.resume_url || '',\n            // Semester-wise CGPA data - use actual backend data\n            semester_cgpas: student.semester_marksheets || [],\n            semester_marksheets: student.semester_marksheets || []\n        });\n    // Debounce search term\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"StudentManagement.useEffect.timer\": ()=>{\n                    setDebouncedSearchTerm(searchTerm);\n                }\n            }[\"StudentManagement.useEffect.timer\"], 300); // 300ms delay\n            return ({\n                \"StudentManagement.useEffect\": ()=>clearTimeout(timer)\n            })[\"StudentManagement.useEffect\"];\n        }\n    }[\"StudentManagement.useEffect\"], [\n        searchTerm\n    ]);\n    // Fetch students with server-side pagination and filtering\n    const fetchStudents = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1;\n        try {\n            setLoading(true);\n            setError(null);\n            setIsRetrying(false);\n            const token = (0,_utils_auth__WEBPACK_IMPORTED_MODULE_3__.getAuthToken)();\n            if (!token) {\n                throw new Error('No authentication token found. Please login first.');\n            }\n            // Build filter parameters for server-side filtering\n            const params = {\n                page,\n                page_size: pageSize\n            };\n            // Add search filter\n            if (debouncedSearchTerm) {\n                params.search = debouncedSearchTerm;\n            }\n            // Add department filter\n            if (selectedDepartment) {\n                params.department = selectedDepartment;\n            }\n            // Add year filter (convert to passout year if needed)\n            if (selectedYear !== 'all') {\n                params.year = selectedYear;\n            }\n            // Add CGPA filters\n            if (cgpaMin) {\n                params.cgpa_min = cgpaMin;\n            }\n            if (cgpaMax) {\n                params.cgpa_max = cgpaMax;\n            }\n            // Fetch data from optimized API\n            const response = await _api_optimized__WEBPACK_IMPORTED_MODULE_2__.studentsAPI.getStudents(params);\n            // Transform the data\n            const transformedStudents = response.data.map(transformStudentData);\n            setStudents(transformedStudents);\n            setCurrentPage(page);\n            setTotalPages(response.pagination.total_pages);\n            setTotalStudents(response.pagination.total_count);\n            setLoading(false);\n        } catch (err) {\n            var _err_response, _err_response1;\n            console.error('Error fetching students:', err);\n            if (((_err_response = err.response) === null || _err_response === void 0 ? void 0 : _err_response.status) === 401) {\n                setError('Authentication failed. Please login again.');\n            } else if (((_err_response1 = err.response) === null || _err_response1 === void 0 ? void 0 : _err_response1.status) === 403) {\n                setError('You do not have permission to view students. Admin access required.');\n            } else if (err.message.includes('token')) {\n                setError('Please login to access student management.');\n            } else {\n                setError(\"Error: \".concat(err.message));\n            }\n            setStudents([]);\n            setLoading(false);\n        }\n    };\n    // Helper function to determine year from branch (you can customize this logic)\n    const getYearFromBranch = (branch, student)=>{\n        if (student && student.joining_year && student.passout_year) {\n            return \"\".concat(student.joining_year, \"-\").concat(student.passout_year);\n        }\n        return 'N/A';\n    };\n    // Initial data fetch\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            fetchStudents();\n        }\n    }[\"StudentManagement.useEffect\"], []);\n    // Add this useEffect after your existing useEffect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            // Check if user is authenticated\n            const token = (0,_utils_auth__WEBPACK_IMPORTED_MODULE_3__.getAuthToken)();\n            if (!token) {\n                // Redirect to login page or show login prompt\n                setError('Please login to access student management.');\n                setLoading(false);\n                return;\n            }\n            fetchStudents();\n        }\n    }[\"StudentManagement.useEffect\"], []);\n    // Helper function to extract year from student ID (assuming format like CS2021001)\n    const getYearFromStudentId = (studentId)=>{\n        if (studentId && studentId.length >= 6) {\n            const yearPart = studentId.substring(2, 6);\n            if (!isNaN(yearPart)) {\n                return \"\".concat(4 - (new Date().getFullYear() - parseInt(yearPart)), \"th Year\");\n            }\n        }\n        return 'Unknown';\n    };\n    // Close dropdown when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"StudentManagement.useEffect.handleClickOutside\": (event)=>{\n                    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                        setIsYearDropdownOpen(false);\n                    }\n                }\n            }[\"StudentManagement.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"StudentManagement.useEffect\": ()=>{\n                    document.removeEventListener('mousedown', handleClickOutside);\n                }\n            })[\"StudentManagement.useEffect\"];\n        }\n    }[\"StudentManagement.useEffect\"], []);\n    // Get available years from students data\n    const getAvailableYears = (studentsData)=>{\n        const years = [\n            ...new Set(studentsData.map((student)=>student.year).filter((year)=>year && year !== 'N/A'))\n        ];\n        return years.sort();\n    };\n    // Get department statistics\n    const getDepartmentStats = (studentsData)=>{\n        const stats = {};\n        studentsData.forEach((student)=>{\n            if (student.department && student.department !== 'N/A') {\n                stats[student.department] = (stats[student.department] || 0) + 1;\n            }\n        });\n        return Object.entries(stats).map((param)=>{\n            let [department, count] = param;\n            return {\n                department,\n                count\n            };\n        });\n    };\n    // This function is no longer needed with server-side pagination\n    // Available years will be fetched from the backend\n    // Filter students for selected department and passout year\n    const getFilteredStudents = ()=>{\n        let filtered = allStudents;\n        if (selectedDepartment) {\n            filtered = filtered.filter((s)=>s.department === selectedDepartment);\n        }\n        if (selectedPassoutYear) {\n            filtered = filtered.filter((s)=>{\n                if (!s.year || s.year === 'N/A') return false;\n                const parts = s.year.split('-');\n                return parts.length === 2 && parts[1] === String(selectedPassoutYear);\n            });\n        }\n        if (debouncedSearchTerm) {\n            const searchLower = debouncedSearchTerm.toLowerCase();\n            filtered = filtered.filter((student)=>student.name.toLowerCase().includes(searchLower) || student.rollNumber.toLowerCase().includes(searchLower));\n        }\n        // CGPA filter\n        filtered = filtered.filter((student)=>{\n            const cgpa = parseFloat(student.cgpa);\n            if (cgpaMin && (isNaN(cgpa) || cgpa < parseFloat(cgpaMin))) return false;\n            if (cgpaMax && (isNaN(cgpa) || cgpa > parseFloat(cgpaMax))) return false;\n            return true;\n        });\n        return filtered;\n    };\n    // Update filters and refetch when dependencies change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            fetchStudents(1); // Reset to page 1 when filters change\n        }\n    }[\"StudentManagement.useEffect\"], [\n        selectedDepartment,\n        selectedYear,\n        debouncedSearchTerm,\n        selectedPassoutYear,\n        cgpaMin,\n        cgpaMax,\n        pageSize\n    ]);\n    // Filter students based on selected department, year, and search term\n    const filteredStudents = students; // Students are already filtered in fetchStudents\n    const handleStudentClick = (student)=>{\n        setSelectedStudent(student);\n        setEditedStudent({\n            ...student\n        });\n        setIsEditing(false);\n    };\n    const handleBackToList = ()=>{\n        setSelectedStudent(null);\n        setIsEditing(false);\n        setEditedStudent(null);\n    };\n    const handleBackToDepartments = ()=>{\n        setSelectedDepartment(null);\n        setSelectedYear('all');\n        setSearchTerm('');\n    };\n    const handleEdit = ()=>{\n        setIsEditing(true);\n    };\n    const handleSave = async ()=>{\n        try {\n            setLoading(true);\n            // Helper function to clean data\n            const cleanValue = (value)=>{\n                if (value === '' || value === null || value === undefined) {\n                    return null;\n                }\n                // Handle string values\n                if (typeof value === 'string') {\n                    const trimmed = value.trim();\n                    return trimmed === '' ? null : trimmed;\n                }\n                return value;\n            };\n            // Helper function to clean numeric values\n            const cleanNumericValue = (value)=>{\n                if (value === '' || value === null || value === undefined) {\n                    return null;\n                }\n                if (typeof value === 'string') {\n                    const trimmed = value.trim();\n                    if (trimmed === '') return null;\n                    const parsed = parseInt(trimmed);\n                    return isNaN(parsed) ? null : parsed;\n                }\n                if (typeof value === 'number') {\n                    return isNaN(value) ? null : value;\n                }\n                return null;\n            };\n            // Helper function to clean string values specifically\n            const cleanStringValue = (value)=>{\n                if (value === '' || value === null || value === undefined) {\n                    return '';\n                }\n                return typeof value === 'string' ? value.trim() : String(value).trim();\n            };\n            // Split the name properly\n            const nameParts = editedStudent.name ? editedStudent.name.trim().split(' ') : [];\n            const firstName = nameParts[0] || '';\n            const lastName = nameParts.slice(1).join(' ') || '';\n            // Prepare the data for backend update\n            const updateData = {\n                // Basic information - ensure strings are not empty\n                first_name: cleanStringValue(firstName),\n                last_name: cleanStringValue(lastName),\n                student_id: cleanStringValue(editedStudent.rollNumber),\n                contact_email: cleanValue(editedStudent.email),\n                phone: cleanStringValue(editedStudent.phone),\n                branch: cleanStringValue(editedStudent.department),\n                gpa: cleanStringValue(editedStudent.gpa),\n                // Academic details - these should be integers\n                joining_year: cleanNumericValue(editedStudent.joining_year),\n                passout_year: cleanNumericValue(editedStudent.passout_year),\n                // Personal details\n                date_of_birth: cleanValue(editedStudent.dateOfBirth),\n                address: cleanStringValue(editedStudent.address),\n                city: cleanStringValue(editedStudent.city),\n                district: cleanStringValue(editedStudent.district),\n                state: cleanStringValue(editedStudent.state),\n                pincode: cleanStringValue(editedStudent.pincode),\n                country: cleanStringValue(editedStudent.country),\n                parent_contact: cleanStringValue(editedStudent.parentContact),\n                education: cleanStringValue(editedStudent.education),\n                skills: Array.isArray(editedStudent.skills) ? editedStudent.skills.filter((skill)=>skill && skill.trim()).join(', ') : cleanStringValue(editedStudent.skills),\n                // Academic scores - all as strings to match model\n                tenth_cgpa: cleanStringValue(editedStudent.tenth_cgpa),\n                tenth_percentage: cleanStringValue(editedStudent.tenth_percentage),\n                tenth_board: cleanStringValue(editedStudent.tenth_board),\n                tenth_school: cleanStringValue(editedStudent.tenth_school),\n                tenth_year_of_passing: cleanStringValue(editedStudent.tenth_year_of_passing),\n                tenth_location: cleanStringValue(editedStudent.tenth_location),\n                tenth_specialization: cleanStringValue(editedStudent.tenth_specialization),\n                twelfth_cgpa: cleanStringValue(editedStudent.twelfth_cgpa),\n                twelfth_percentage: cleanStringValue(editedStudent.twelfth_percentage),\n                twelfth_board: cleanStringValue(editedStudent.twelfth_board),\n                twelfth_school: cleanStringValue(editedStudent.twelfth_school),\n                twelfth_year_of_passing: cleanStringValue(editedStudent.twelfth_year_of_passing),\n                twelfth_location: cleanStringValue(editedStudent.twelfth_location),\n                twelfth_specialization: cleanStringValue(editedStudent.twelfth_specialization)\n            };\n            // Add semester CGPAs if they exist\n            if (editedStudent.semester_cgpas && Array.isArray(editedStudent.semester_cgpas)) {\n                editedStudent.semester_cgpas.forEach((semesterData)=>{\n                    if (semesterData.semester >= 1 && semesterData.semester <= 8 && semesterData.cgpa) {\n                        updateData[\"semester\".concat(semesterData.semester, \"_cgpa\")] = cleanStringValue(semesterData.cgpa);\n                    }\n                });\n            }\n            // Remove empty string values but keep nulls for proper field clearing\n            const cleanedUpdateData = Object.fromEntries(Object.entries(updateData).filter((param)=>{\n                let [key, value] = param;\n                // Keep nulls for clearing fields, remove empty strings except for required fields\n                const requiredFields = [\n                    'first_name',\n                    'last_name',\n                    'student_id',\n                    'gpa'\n                ];\n                if (requiredFields.includes(key)) {\n                    return value !== null && value !== undefined;\n                }\n                return value !== null && value !== undefined && value !== '';\n            }));\n            // Ensure required fields have default values if missing\n            if (!cleanedUpdateData.first_name) cleanedUpdateData.first_name = 'Student';\n            if (!cleanedUpdateData.last_name) cleanedUpdateData.last_name = '';\n            if (!cleanedUpdateData.student_id) cleanedUpdateData.student_id = \"TEMP_\".concat(Date.now());\n            if (!cleanedUpdateData.gpa) cleanedUpdateData.gpa = '0.0';\n            // Debug logging\n            console.log('Original editedStudent:', editedStudent);\n            console.log('Update data being sent:', cleanedUpdateData);\n            console.log('Student ID:', editedStudent.id);\n            // Make API call to update student\n            const updatedStudent = await _api_optimized__WEBPACK_IMPORTED_MODULE_2__.studentsAPI.updateStudent(editedStudent.id, cleanedUpdateData);\n            // Update the student in the list with the response data\n            const updatedStudentData = {\n                ...editedStudent,\n                ...updatedStudent,\n                name: \"\".concat(updatedStudent.first_name || '', \" \").concat(updatedStudent.last_name || '').trim(),\n                rollNumber: updatedStudent.student_id,\n                email: updatedStudent.contact_email,\n                department: updatedStudent.branch,\n                gpa: updatedStudent.gpa\n            };\n            setStudents((prev)=>prev.map((student)=>student.id === editedStudent.id ? updatedStudentData : student));\n            setSelectedStudent(updatedStudentData);\n            setIsEditing(false);\n            // Show success message\n            alert('Student profile updated successfully!');\n        } catch (error) {\n            var _error_response;\n            console.error('Error updating student:', error);\n            // More detailed error logging\n            if (error.response) {\n                console.error('Error response status:', error.response.status);\n                console.error('Error response data:', error.response.data);\n                console.error('Error response headers:', error.response.headers);\n            }\n            let errorMessage = 'Failed to update student profile. Please try again.';\n            if ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) {\n                // Handle validation errors\n                if (typeof error.response.data === 'object') {\n                    const errorDetails = [];\n                    for (const [field, messages] of Object.entries(error.response.data)){\n                        if (Array.isArray(messages)) {\n                            errorDetails.push(\"\".concat(field, \": \").concat(messages.join(', ')));\n                        } else {\n                            errorDetails.push(\"\".concat(field, \": \").concat(messages));\n                        }\n                    }\n                    if (errorDetails.length > 0) {\n                        errorMessage = \"Validation errors:\\n\".concat(errorDetails.join('\\n'));\n                    }\n                } else if (error.response.data.detail) {\n                    errorMessage = error.response.data.detail;\n                } else if (error.response.data.message) {\n                    errorMessage = error.response.data.message;\n                }\n            }\n            alert(\"Update failed: \".concat(errorMessage));\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleCancel = ()=>{\n        setEditedStudent({\n            ...selectedStudent\n        });\n        setIsEditing(false);\n    };\n    const handleInputChange = (field, value)=>{\n        setEditedStudent((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    // Handle retry button click\n    const handleRetry = ()=>{\n        setIsRetrying(true);\n        fetchStudents();\n    };\n    // Help developers find the correct API endpoint\n    const debugBackend = ()=>{\n        window.open('http://localhost:8000/admin/');\n    };\n    const handleSearch = ()=>{\n        // Force immediate search without waiting for debounce\n        setDebouncedSearchTerm(searchTerm);\n        setCurrentPage(1);\n    };\n    // Handle pagination\n    const handlePageChange = (newPage)=>{\n        if (newPage >= 1 && newPage <= totalPages) {\n            fetchStudents(newPage);\n        }\n    };\n    // Handle search input change\n    const handleSearchInputChange = (e)=>{\n        setSearchTerm(e.target.value);\n    // Don't trigger immediate search, let debounce handle it\n    };\n    // Handle search input key press\n    const handleSearchKeyDown = (e)=>{\n        if (e.key === 'Enter') {\n            e.preventDefault();\n            handleSearch();\n        }\n    };\n    if (loading) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 p-6 ml-20 overflow-y-auto h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center h-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mb-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                    lineNumber: 575,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-gray-600\",\n                    children: \"Loading students...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                    lineNumber: 576,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n            lineNumber: 574,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n        lineNumber: 573,\n        columnNumber: 5\n    }, this);\n    if (error && students.length === 0) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 p-6 ml-20 overflow-y-auto h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center h-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-red-500 mb-4 text-center max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-semibold text-lg mb-2\",\n                            children: \"Access Error\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 585,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 586,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 text-sm text-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Possible solutions:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                    lineNumber: 588,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"list-disc list-inside mt-2 text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Make sure you're logged in with admin credentials\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                            lineNumber: 590,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Check if your session has expired\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                            lineNumber: 591,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Verify Django server is running on port 8000\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                            lineNumber: 592,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Ensure proper permissions are set in Django\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                            lineNumber: 593,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                    lineNumber: 589,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 587,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                    lineNumber: 584,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-3 mt-4\",\n                    children: [\n                        !error.includes('login') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleRetry,\n                            className: \"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                            disabled: isRetrying,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_GraduationCap_RefreshCw_Save_Search_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-4 h-4 \".concat(isRetrying ? 'animate-spin' : '')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                    lineNumber: 604,\n                                    columnNumber: 15\n                                }, this),\n                                isRetrying ? 'Retrying...' : 'Retry'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 599,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>window.location.href = '/login',\n                            className: \"flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_GraduationCap_RefreshCw_Save_Search_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                    lineNumber: 612,\n                                    columnNumber: 13\n                                }, this),\n                                \"Go to Login\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 608,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                    lineNumber: 597,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n            lineNumber: 583,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n        lineNumber: 582,\n        columnNumber: 5\n    }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 p-6 ml-20 overflow-y-auto h-full\",\n        children: !selectedStudent ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: !selectedDepartment ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DepartmentCards__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                departmentOptions: departmentOptions,\n                departmentStats: departmentStats,\n                allStudents: allStudents,\n                onSelect: setSelectedDepartment\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                lineNumber: 625,\n                columnNumber: 13\n            }, this) : !selectedPassoutYear ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PassoutYearCards__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                departmentLabel: (_departmentOptions_find = departmentOptions.find((d)=>d.value === selectedDepartment)) === null || _departmentOptions_find === void 0 ? void 0 : _departmentOptions_find.label,\n                onBack: handleBackToDepartments,\n                getAvailablePassoutYears: getAvailablePassoutYears,\n                allStudents: allStudents,\n                selectedDepartment: selectedDepartment,\n                onSelectYear: setSelectedPassoutYear\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                lineNumber: 632,\n                columnNumber: 13\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StudentList__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                departmentLabel: (_departmentOptions_find1 = departmentOptions.find((d)=>d.value === selectedDepartment)) === null || _departmentOptions_find1 === void 0 ? void 0 : _departmentOptions_find1.label,\n                passoutYear: selectedPassoutYear,\n                onBack: ()=>setSelectedPassoutYear(null),\n                searchTerm: searchTerm,\n                handleSearchInputChange: handleSearchInputChange,\n                handleSearchKeyDown: handleSearchKeyDown,\n                cgpaMin: cgpaMin,\n                setCgpaMin: setCgpaMin,\n                cgpaMax: cgpaMax,\n                setCgpaMax: setCgpaMax,\n                handleSearch: handleSearch,\n                getFilteredStudents: getFilteredStudents,\n                currentPage: currentPage,\n                handlePageChange: handlePageChange,\n                handleStudentClick: handleStudentClick,\n                loading: loading\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                lineNumber: 641,\n                columnNumber: 13\n            }, this)\n        }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StudentProfile__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            selectedStudent: selectedStudent,\n            editedStudent: editedStudent,\n            isEditing: isEditing,\n            handleBackToList: handleBackToList,\n            handleEdit: handleEdit,\n            handleSave: handleSave,\n            handleCancel: handleCancel,\n            handleInputChange: handleInputChange,\n            departmentOptions: departmentOptions\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n            lineNumber: 662,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n        lineNumber: 621,\n        columnNumber: 5\n    }, this);\n}\n_s(StudentManagement, \"MByQbfljhdtahv1cKIsASH8Inzo=\");\n_c = StudentManagement;\nvar _c;\n$RefreshReg$(_c, \"StudentManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/student-management/page.jsx\n"));

/***/ })

});