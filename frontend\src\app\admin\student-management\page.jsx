'use client';

import {
  ArrowLeft,
  Calendar,
  RefreshCw,
  Save,
  Search,
  User,
  X,
  GraduationCap
} from "lucide-react";
import { useEffect, useRef, useState } from 'react';
import { studentsAPI } from '../../../api/optimized';
import { getAuthToken } from '../../../utils/auth';
import CustomDropdown from './StudentDropdown';
import StudentProfile from './StudentProfile';
import DepartmentCards from './DepartmentCards';
import PassoutYearCards from './PassoutYearCards';
import StudentList from './StudentList';

export default function StudentManagement() {
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [selectedDepartment, setSelectedDepartment] = useState(null);
  const [selectedYear, setSelectedYear] = useState('all');
  const [selectedStudent, setSelectedStudent] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editedStudent, setEditedStudent] = useState(null);
  const dropdownRef = useRef(null);
  const [isYearDropdownOpen, setIsYearDropdownOpen] = useState(false);
  const [students, setStudents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isRetrying, setIsRetrying] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalStudents, setTotalStudents] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [availableYears, setAvailableYears] = useState([]);
  const [departmentStats, setDepartmentStats] = useState([]);
  const [selectedPassoutYear, setSelectedPassoutYear] = useState(null);
  const [cgpaMin, setCgpaMin] = useState('');
  const [cgpaMax, setCgpaMax] = useState('');

  // Dropdown options
  const departmentOptions = [
    { value: 'Computer Science', label: 'Computer Science' },
    { value: 'Electronics', label: 'Electronics' },
    { value: 'Mechanical', label: 'Mechanical' },
    { value: 'Civil', label: 'Civil' },
    { value: 'Electrical', label: 'Electrical' },
    { value: 'Information Technology', label: 'Information Technology' },
    { value: 'Chemical', label: 'Chemical' },
    { value: 'Biotechnology', label: 'Biotechnology' }
  ];

  // Transform student data from API response
  const transformStudentData = (student) => ({
    id: student.id,
    rollNumber: student.student_id || 'N/A',
    name: `${student.first_name || ''} ${student.last_name || ''}`.trim() || 'Unknown',
    email: student.contact_email || student.email || 'N/A',
    phone: student.phone || 'N/A',
    department: student.branch || 'N/A',
    year: getYearFromBranch(student.branch, student),
    cgpa: student.gpa || 'N/A',
    gpa: student.gpa || 'N/A', // Overall CGPA from database
    address: student.address || 'N/A',
    dateOfBirth: student.date_of_birth || '',
    parentContact: student.parent_contact || 'N/A',
    education: student.education || 'N/A',
    skills: student.skills || [],

    // Academic details
        joining_year: student.joining_year || student.admission_year || '',
        passout_year: student.passout_year || student.graduation_year || '',

        // Class XII details
        twelfth_cgpa: student.twelfth_cgpa || student.class_12_cgpa || '',
        twelfth_percentage: student.twelfth_percentage || student.class_12_percentage || '',
        twelfth_year_of_passing: student.twelfth_year_of_passing || student.class_12_year || '',
        twelfth_school: student.twelfth_school || student.class_12_school || '',
        twelfth_board: student.twelfth_board || student.class_12_board || '',
        twelfth_location: student.twelfth_location || student.class_12_location || '',
        twelfth_specialization: student.twelfth_specialization || student.class_12_stream || '',

        // Class X details
        tenth_cgpa: student.tenth_cgpa || student.class_10_cgpa || '',
        tenth_percentage: student.tenth_percentage || student.class_10_percentage || '',
        tenth_year_of_passing: student.tenth_year_of_passing || student.class_10_year || '',
        tenth_school: student.tenth_school || student.class_10_school || '',
        tenth_board: student.tenth_board || student.class_10_board || '',
        tenth_location: student.tenth_location || student.class_10_location || '',
        tenth_specialization: student.tenth_specialization || student.class_10_stream || '',

        // Address details
        city: student.city || '',
        district: student.district || '',
        state: student.state || '',
        pincode: student.pincode || student.pin_code || '',
        country: student.country || 'India',

        // Certificate URLs
        tenth_certificate: student.tenth_certificate || student.class_10_certificate || '',
        twelfth_certificate: student.twelfth_certificate || student.class_12_certificate || '',
        tenth_certificate_url: student.tenth_certificate_url || student.class_10_certificate_url || '',
        twelfth_certificate_url: student.twelfth_certificate_url || student.class_12_certificate_url || '',

        // Resume details
        resume: student.resume || '',
        resume_url: student.resume_url || '',

        // Semester-wise CGPA data - use actual backend data
        semester_cgpas: student.semester_marksheets || [],
        semester_marksheets: student.semester_marksheets || [],
      });

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 300); // 300ms delay

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Fetch students with server-side pagination and filtering
  const fetchStudents = async (page = 1) => {
    try {
      setLoading(true);
      setError(null);
      setIsRetrying(false);

      const token = getAuthToken();
      if (!token) {
        throw new Error('No authentication token found. Please login first.');
      }

      // Build filter parameters for server-side filtering
      const params = {
        page,
        page_size: pageSize,
      };

      // Add search filter
      if (debouncedSearchTerm) {
        params.search = debouncedSearchTerm;
      }

      // Add department filter
      if (selectedDepartment) {
        params.department = selectedDepartment;
      }

      // Add year filter (convert to passout year if needed)
      if (selectedYear !== 'all') {
        params.year = selectedYear;
      }

      // Add CGPA filters
      if (cgpaMin) {
        params.cgpa_min = cgpaMin;
      }
      if (cgpaMax) {
        params.cgpa_max = cgpaMax;
      }

      // Fetch data from optimized API
      const response = await studentsAPI.getStudents(params);

      // Transform the data
      const transformedStudents = response.data.map(transformStudentData);

      setStudents(transformedStudents);
      setCurrentPage(page);
      setTotalPages(response.pagination.total_pages);
      setTotalStudents(response.pagination.total_count);

      setLoading(false);
    } catch (err) {
      console.error('Error fetching students:', err);

      if (err.response?.status === 401) {
        setError('Authentication failed. Please login again.');
      } else if (err.response?.status === 403) {
        setError('You do not have permission to view students. Admin access required.');
      } else if (err.message.includes('token')) {
        setError('Please login to access student management.');
      } else {
        setError(`Error: ${err.message}`);
      }

      setStudents([]);
      setLoading(false);
    }
  };

  // Helper function to determine year from branch (you can customize this logic)
  const getYearFromBranch = (branch, student) => {
    if (student && student.joining_year && student.passout_year) {
      return `${student.joining_year}-${student.passout_year}`;
    }
    return 'N/A';
  };

  // Initial data fetch
  useEffect(() => {
    fetchStudents();
  }, []);

  // Add this useEffect after your existing useEffect
  useEffect(() => {
    // Check if user is authenticated
    const token = getAuthToken();
    if (!token) {
      // Redirect to login page or show login prompt
      setError('Please login to access student management.');
      setLoading(false);
      return;
    }
    
    fetchStudents();
  }, []);

  // Helper function to extract year from student ID (assuming format like *********)
  const getYearFromStudentId = (studentId) => {
    if (studentId && studentId.length >= 6) {
      const yearPart = studentId.substring(2, 6);
      if (!isNaN(yearPart)) {
        return `${4 - (new Date().getFullYear() - parseInt(yearPart))}th Year`;
      }
    }
    return 'Unknown';
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsYearDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Get available years from students data
  const getAvailableYears = (studentsData) => {
    const years = [...new Set(studentsData.map(student => student.year).filter(year => year && year !== 'N/A'))];
    return years.sort();
  };

  // Get department statistics
  const getDepartmentStats = (studentsData) => {
    const stats = {};
    studentsData.forEach(student => {
      if (student.department && student.department !== 'N/A') {
        stats[student.department] = (stats[student.department] || 0) + 1;
      }
    });
    return Object.entries(stats).map(([department, count]) => ({ department, count }));
  };

  // This function is no longer needed with server-side pagination
  // Available years will be fetched from the backend

  // Filtering is now handled server-side in fetchStudents

  // Update filters and refetch when dependencies change
  useEffect(() => {
    fetchStudents(1); // Reset to page 1 when filters change
  }, [selectedDepartment, selectedYear, debouncedSearchTerm, selectedPassoutYear, cgpaMin, cgpaMax, pageSize]);

  // Filter students based on selected department, year, and search term
  const filteredStudents = students; // Students are already filtered in fetchStudents

  const handleStudentClick = (student) => {
    setSelectedStudent(student);
    setEditedStudent({ ...student });
    setIsEditing(false);
  };

  const handleBackToList = () => {
    setSelectedStudent(null);
    setIsEditing(false);
    setEditedStudent(null);
  };

  const handleBackToDepartments = () => {
    setSelectedDepartment(null);
    setSelectedYear('all');
    setSearchTerm('');
  };

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSave = async () => {
    try {
      setLoading(true);

      // Helper function to clean data
      const cleanValue = (value) => {
        if (value === '' || value === null || value === undefined) {
          return null;
        }
        // Handle string values
        if (typeof value === 'string') {
          const trimmed = value.trim();
          return trimmed === '' ? null : trimmed;
        }
        return value;
      };

      // Helper function to clean numeric values
      const cleanNumericValue = (value) => {
        if (value === '' || value === null || value === undefined) {
          return null;
        }
        if (typeof value === 'string') {
          const trimmed = value.trim();
          if (trimmed === '') return null;
          const parsed = parseInt(trimmed);
          return isNaN(parsed) ? null : parsed;
        }
        if (typeof value === 'number') {
          return isNaN(value) ? null : value;
        }
        return null;
      };

      // Helper function to clean string values specifically
      const cleanStringValue = (value) => {
        if (value === '' || value === null || value === undefined) {
          return '';
        }
        return typeof value === 'string' ? value.trim() : String(value).trim();
      };

      // Split the name properly
      const nameParts = editedStudent.name ? editedStudent.name.trim().split(' ') : [];
      const firstName = nameParts[0] || '';
      const lastName = nameParts.slice(1).join(' ') || '';

      // Prepare the data for backend update
      const updateData = {
        // Basic information - ensure strings are not empty
        first_name: cleanStringValue(firstName),
        last_name: cleanStringValue(lastName),
        student_id: cleanStringValue(editedStudent.rollNumber),
        contact_email: cleanValue(editedStudent.email),
        phone: cleanStringValue(editedStudent.phone),
        branch: cleanStringValue(editedStudent.department),
        gpa: cleanStringValue(editedStudent.gpa), // Overall CGPA as string

        // Academic details - these should be integers
        joining_year: cleanNumericValue(editedStudent.joining_year),
        passout_year: cleanNumericValue(editedStudent.passout_year),

        // Personal details
        date_of_birth: cleanValue(editedStudent.dateOfBirth),
        address: cleanStringValue(editedStudent.address),
        city: cleanStringValue(editedStudent.city),
        district: cleanStringValue(editedStudent.district),
        state: cleanStringValue(editedStudent.state),
        pincode: cleanStringValue(editedStudent.pincode),
        country: cleanStringValue(editedStudent.country),
        parent_contact: cleanStringValue(editedStudent.parentContact),
        education: cleanStringValue(editedStudent.education),
        skills: Array.isArray(editedStudent.skills) 
          ? editedStudent.skills.filter(skill => skill && skill.trim()).join(', ')
          : cleanStringValue(editedStudent.skills),

        // Academic scores - all as strings to match model
        tenth_cgpa: cleanStringValue(editedStudent.tenth_cgpa),
        tenth_percentage: cleanStringValue(editedStudent.tenth_percentage),
        tenth_board: cleanStringValue(editedStudent.tenth_board),
        tenth_school: cleanStringValue(editedStudent.tenth_school),
        tenth_year_of_passing: cleanStringValue(editedStudent.tenth_year_of_passing),
        tenth_location: cleanStringValue(editedStudent.tenth_location),
        tenth_specialization: cleanStringValue(editedStudent.tenth_specialization),

        twelfth_cgpa: cleanStringValue(editedStudent.twelfth_cgpa),
        twelfth_percentage: cleanStringValue(editedStudent.twelfth_percentage),
        twelfth_board: cleanStringValue(editedStudent.twelfth_board),
        twelfth_school: cleanStringValue(editedStudent.twelfth_school),
        twelfth_year_of_passing: cleanStringValue(editedStudent.twelfth_year_of_passing),
        twelfth_location: cleanStringValue(editedStudent.twelfth_location),
        twelfth_specialization: cleanStringValue(editedStudent.twelfth_specialization),
      };

      // Add semester CGPAs if they exist
      if (editedStudent.semester_cgpas && Array.isArray(editedStudent.semester_cgpas)) {
        editedStudent.semester_cgpas.forEach(semesterData => {
          if (semesterData.semester >= 1 && semesterData.semester <= 8 && semesterData.cgpa) {
            updateData[`semester${semesterData.semester}_cgpa`] = cleanStringValue(semesterData.cgpa);
          }
        });
      }

      // Remove empty string values but keep nulls for proper field clearing
      const cleanedUpdateData = Object.fromEntries(
        Object.entries(updateData).filter(([key, value]) => {
          // Keep nulls for clearing fields, remove empty strings except for required fields
          const requiredFields = ['first_name', 'last_name', 'student_id', 'gpa'];
          if (requiredFields.includes(key)) {
            return value !== null && value !== undefined;
          }
          return value !== null && value !== undefined && value !== '';
        })
      );

      // Ensure required fields have default values if missing
      if (!cleanedUpdateData.first_name) cleanedUpdateData.first_name = 'Student';
      if (!cleanedUpdateData.last_name) cleanedUpdateData.last_name = '';
      if (!cleanedUpdateData.student_id) cleanedUpdateData.student_id = `TEMP_${Date.now()}`;
      if (!cleanedUpdateData.gpa) cleanedUpdateData.gpa = '0.0';

      // Debug logging
      console.log('Original editedStudent:', editedStudent);
      console.log('Update data being sent:', cleanedUpdateData);
      console.log('Student ID:', editedStudent.id);

      // Make API call to update student
      const updatedStudent = await studentsAPI.updateStudent(editedStudent.id, cleanedUpdateData);

      // Update the student in the list with the response data
      const updatedStudentData = {
        ...editedStudent,
        ...updatedStudent,
        name: `${updatedStudent.first_name || ''} ${updatedStudent.last_name || ''}`.trim(),
        rollNumber: updatedStudent.student_id,
        email: updatedStudent.contact_email,
        department: updatedStudent.branch,
        gpa: updatedStudent.gpa,
      };

      setStudents(prev =>
        prev.map(student =>
          student.id === editedStudent.id ? updatedStudentData : student
        )
      );

      setSelectedStudent(updatedStudentData);
      setIsEditing(false);

      // Show success message
      alert('Student profile updated successfully!');

    } catch (error) {
      console.error('Error updating student:', error);
      
      // More detailed error logging
      if (error.response) {
        console.error('Error response status:', error.response.status);
        console.error('Error response data:', error.response.data);
        console.error('Error response headers:', error.response.headers);
      }
      
      let errorMessage = 'Failed to update student profile. Please try again.';
      
      if (error.response?.data) {
        // Handle validation errors
        if (typeof error.response.data === 'object') {
          const errorDetails = [];
          for (const [field, messages] of Object.entries(error.response.data)) {
            if (Array.isArray(messages)) {
              errorDetails.push(`${field}: ${messages.join(', ')}`);
            } else {
              errorDetails.push(`${field}: ${messages}`);
            }
          }
          if (errorDetails.length > 0) {
            errorMessage = `Validation errors:\n${errorDetails.join('\n')}`;
          }
        } else if (error.response.data.detail) {
          errorMessage = error.response.data.detail;
        } else if (error.response.data.message) {
          errorMessage = error.response.data.message;
        }
      }
      
      alert(`Update failed: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setEditedStudent({ ...selectedStudent });
    setIsEditing(false);
  };

  const handleInputChange = (field, value) => {
    setEditedStudent(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle retry button click
  const handleRetry = () => {
    setIsRetrying(true);
    fetchStudents();
  };

  // Help developers find the correct API endpoint
  const debugBackend = () => {
    window.open('http://localhost:8000/admin/');
  };

  const handleSearch = () => {
    // Force immediate search without waiting for debounce
    setDebouncedSearchTerm(searchTerm);
    setCurrentPage(1);
  };

  // Handle pagination
  const handlePageChange = (newPage) => {
    if (newPage >= 1 && newPage <= totalPages) {
      fetchStudents(newPage);
    }
  };

  // Handle search input change
  const handleSearchInputChange = (e) => {
    setSearchTerm(e.target.value);
    // Don't trigger immediate search, let debounce handle it
  };

  // Handle search input key press
  const handleSearchKeyDown = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSearch();
    }
  };

  if (loading) return (
    <div className="flex-1 p-6 ml-20 overflow-y-auto h-full">
      <div className="flex flex-col items-center justify-center h-full">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mb-4"></div>
        <div className="text-gray-600">Loading students...</div>
      </div>
    </div>
  );
  
  if (error && students.length === 0) return (
    <div className="flex-1 p-6 ml-20 overflow-y-auto h-full">
      <div className="flex flex-col items-center justify-center h-full">
        <div className="text-red-500 mb-4 text-center max-w-md">
          <p className="font-semibold text-lg mb-2">Access Error</p>
          <p>{error}</p>
          <div className="mt-4 text-sm text-gray-600">
            <p>Possible solutions:</p>
            <ul className="list-disc list-inside mt-2 text-left">
              <li>Make sure you're logged in with admin credentials</li>
              <li>Check if your session has expired</li>
              <li>Verify Django server is running on port 8000</li>
              <li>Ensure proper permissions are set in Django</li>
            </ul>
          </div>
        </div>
        <div className="flex gap-3 mt-4">
          {!error.includes('login') && (
            <button 
              onClick={handleRetry}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              disabled={isRetrying}
            >
              <RefreshCw className={`w-4 h-4 ${isRetrying ? 'animate-spin' : ''}`} />
              {isRetrying ? 'Retrying...' : 'Retry'}
            </button>
          )}
          <button 
            onClick={() => window.location.href = '/login'}
            className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
          >
            <User className="w-4 h-4" />
            Go to Login
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="flex-1 p-6 ml-20 overflow-y-auto h-full">
      {!selectedStudent ? (
        <>
          {!selectedDepartment ? (
            <DepartmentCards
              departmentOptions={departmentOptions}
              departmentStats={departmentStats}
              allStudents={allStudents}
              onSelect={setSelectedDepartment}
            />
          ) : !selectedPassoutYear ? (
            <PassoutYearCards
              departmentLabel={departmentOptions.find(d => d.value === selectedDepartment)?.label}
              onBack={handleBackToDepartments}
              getAvailablePassoutYears={getAvailablePassoutYears}
              allStudents={allStudents}
              selectedDepartment={selectedDepartment}
              onSelectYear={setSelectedPassoutYear}
            />
          ) : (
            <StudentList
              departmentLabel={departmentOptions.find(d => d.value === selectedDepartment)?.label}
              passoutYear={selectedPassoutYear}
              onBack={() => setSelectedPassoutYear(null)}
              searchTerm={searchTerm}
              handleSearchInputChange={handleSearchInputChange}
              handleSearchKeyDown={handleSearchKeyDown}
              cgpaMin={cgpaMin}
              setCgpaMin={setCgpaMin}
              cgpaMax={cgpaMax}
              setCgpaMax={setCgpaMax}
              handleSearch={handleSearch}
              getFilteredStudents={getFilteredStudents}
              currentPage={currentPage}
              handlePageChange={handlePageChange}
              handleStudentClick={handleStudentClick}
              loading={loading}
            />
          )}
        </>
      ) : (
        <StudentProfile
          selectedStudent={selectedStudent}
          editedStudent={editedStudent}
          isEditing={isEditing}
          handleBackToList={handleBackToList}
          handleEdit={handleEdit}
          handleSave={handleSave}
          handleCancel={handleCancel}
          handleInputChange={handleInputChange}
          departmentOptions={departmentOptions}
        />
      )}
    </div>
  );
}