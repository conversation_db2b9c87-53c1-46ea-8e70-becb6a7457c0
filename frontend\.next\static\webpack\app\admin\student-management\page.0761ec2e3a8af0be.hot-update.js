"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/student-management/page",{

/***/ "(app-pages-browser)/./src/app/admin/student-management/page.jsx":
/*!***************************************************!*\
  !*** ./src/app/admin/student-management/page.jsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StudentManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_GraduationCap_RefreshCw_Save_Search_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,GraduationCap,RefreshCw,Save,Search,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_GraduationCap_RefreshCw_Save_Search_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,GraduationCap,RefreshCw,Save,Search,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _api_optimized__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../api/optimized */ \"(app-pages-browser)/./src/api/optimized.js\");\n/* harmony import */ var _utils_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../utils/auth */ \"(app-pages-browser)/./src/utils/auth.js\");\n/* harmony import */ var _StudentDropdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./StudentDropdown */ \"(app-pages-browser)/./src/app/admin/student-management/StudentDropdown.jsx\");\n/* harmony import */ var _StudentProfile__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./StudentProfile */ \"(app-pages-browser)/./src/app/admin/student-management/StudentProfile.jsx\");\n/* harmony import */ var _DepartmentCards__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./DepartmentCards */ \"(app-pages-browser)/./src/app/admin/student-management/DepartmentCards.jsx\");\n/* harmony import */ var _PassoutYearCards__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./PassoutYearCards */ \"(app-pages-browser)/./src/app/admin/student-management/PassoutYearCards.jsx\");\n/* harmony import */ var _StudentList__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./StudentList */ \"(app-pages-browser)/./src/app/admin/student-management/StudentList.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction StudentManagement() {\n    var _departmentOptions_find, _departmentOptions_find1;\n    _s();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [debouncedSearchTerm, setDebouncedSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedDepartment, setSelectedDepartment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedYear, setSelectedYear] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [selectedStudent, setSelectedStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editedStudent, setEditedStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isYearDropdownOpen, setIsYearDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allStudents, setAllStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isRetrying, setIsRetrying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalStudents, setTotalStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(10);\n    const [availableYears, setAvailableYears] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [departmentStats, setDepartmentStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedPassoutYear, setSelectedPassoutYear] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [cgpaMin, setCgpaMin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [cgpaMax, setCgpaMax] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Dropdown options\n    const departmentOptions = [\n        {\n            value: 'Computer Science',\n            label: 'Computer Science'\n        },\n        {\n            value: 'Electronics',\n            label: 'Electronics'\n        },\n        {\n            value: 'Mechanical',\n            label: 'Mechanical'\n        },\n        {\n            value: 'Civil',\n            label: 'Civil'\n        },\n        {\n            value: 'Electrical',\n            label: 'Electrical'\n        },\n        {\n            value: 'Information Technology',\n            label: 'Information Technology'\n        },\n        {\n            value: 'Chemical',\n            label: 'Chemical'\n        },\n        {\n            value: 'Biotechnology',\n            label: 'Biotechnology'\n        }\n    ];\n    // Transform student data from API response\n    const transformStudentData = (student)=>({\n            id: student.id,\n            rollNumber: student.student_id || 'N/A',\n            name: \"\".concat(student.first_name || '', \" \").concat(student.last_name || '').trim() || 'Unknown',\n            email: student.contact_email || student.email || 'N/A',\n            phone: student.phone || 'N/A',\n            department: student.branch || 'N/A',\n            year: getYearFromBranch(student.branch, student),\n            cgpa: student.gpa || 'N/A',\n            gpa: student.gpa || 'N/A',\n            address: student.address || 'N/A',\n            dateOfBirth: student.date_of_birth || '',\n            parentContact: student.parent_contact || 'N/A',\n            education: student.education || 'N/A',\n            skills: student.skills || [],\n            // Academic details\n            joining_year: student.joining_year || student.admission_year || '',\n            passout_year: student.passout_year || student.graduation_year || '',\n            // Class XII details\n            twelfth_cgpa: student.twelfth_cgpa || student.class_12_cgpa || '',\n            twelfth_percentage: student.twelfth_percentage || student.class_12_percentage || '',\n            twelfth_year_of_passing: student.twelfth_year_of_passing || student.class_12_year || '',\n            twelfth_school: student.twelfth_school || student.class_12_school || '',\n            twelfth_board: student.twelfth_board || student.class_12_board || '',\n            twelfth_location: student.twelfth_location || student.class_12_location || '',\n            twelfth_specialization: student.twelfth_specialization || student.class_12_stream || '',\n            // Class X details\n            tenth_cgpa: student.tenth_cgpa || student.class_10_cgpa || '',\n            tenth_percentage: student.tenth_percentage || student.class_10_percentage || '',\n            tenth_year_of_passing: student.tenth_year_of_passing || student.class_10_year || '',\n            tenth_school: student.tenth_school || student.class_10_school || '',\n            tenth_board: student.tenth_board || student.class_10_board || '',\n            tenth_location: student.tenth_location || student.class_10_location || '',\n            tenth_specialization: student.tenth_specialization || student.class_10_stream || '',\n            // Address details\n            city: student.city || '',\n            district: student.district || '',\n            state: student.state || '',\n            pincode: student.pincode || student.pin_code || '',\n            country: student.country || 'India',\n            // Certificate URLs\n            tenth_certificate: student.tenth_certificate || student.class_10_certificate || '',\n            twelfth_certificate: student.twelfth_certificate || student.class_12_certificate || '',\n            tenth_certificate_url: student.tenth_certificate_url || student.class_10_certificate_url || '',\n            twelfth_certificate_url: student.twelfth_certificate_url || student.class_12_certificate_url || '',\n            // Resume details\n            resume: student.resume || '',\n            resume_url: student.resume_url || '',\n            // Semester-wise CGPA data - use actual backend data\n            semester_cgpas: student.semester_marksheets || [],\n            semester_marksheets: student.semester_marksheets || []\n        });\n    // Debounce search term\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"StudentManagement.useEffect.timer\": ()=>{\n                    setDebouncedSearchTerm(searchTerm);\n                }\n            }[\"StudentManagement.useEffect.timer\"], 300); // 300ms delay\n            return ({\n                \"StudentManagement.useEffect\": ()=>clearTimeout(timer)\n            })[\"StudentManagement.useEffect\"];\n        }\n    }[\"StudentManagement.useEffect\"], [\n        searchTerm\n    ]);\n    // Fetch students with server-side pagination and filtering\n    const fetchStudents = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1;\n        try {\n            setLoading(true);\n            setError(null);\n            setIsRetrying(false);\n            const token = (0,_utils_auth__WEBPACK_IMPORTED_MODULE_3__.getAuthToken)();\n            if (!token) {\n                throw new Error('No authentication token found. Please login first.');\n            }\n            // Build filter parameters for server-side filtering\n            const params = {\n                page,\n                page_size: pageSize\n            };\n            // Add search filter\n            if (debouncedSearchTerm) {\n                params.search = debouncedSearchTerm;\n            }\n            // Add department filter\n            if (selectedDepartment) {\n                params.department = selectedDepartment;\n            }\n            // Add year filter (convert to passout year if needed)\n            if (selectedYear !== 'all') {\n                params.year = selectedYear;\n            }\n            // Add CGPA filters\n            if (cgpaMin) {\n                params.cgpa_min = cgpaMin;\n            }\n            if (cgpaMax) {\n                params.cgpa_max = cgpaMax;\n            }\n            // Fetch data from optimized API\n            const response = await _api_optimized__WEBPACK_IMPORTED_MODULE_2__.studentsAPI.getStudents(params);\n            // Transform the data\n            const transformedStudents = response.data.map(transformStudentData);\n            setStudents(transformedStudents);\n            setCurrentPage(page);\n            setTotalPages(response.pagination.total_pages);\n            setTotalStudents(response.pagination.total_count);\n            setLoading(false);\n        } catch (err) {\n            var _err_response, _err_response1;\n            console.error('Error fetching students:', err);\n            if (((_err_response = err.response) === null || _err_response === void 0 ? void 0 : _err_response.status) === 401) {\n                setError('Authentication failed. Please login again.');\n            } else if (((_err_response1 = err.response) === null || _err_response1 === void 0 ? void 0 : _err_response1.status) === 403) {\n                setError('You do not have permission to view students. Admin access required.');\n            } else if (err.message.includes('token')) {\n                setError('Please login to access student management.');\n            } else {\n                setError(\"Error: \".concat(err.message));\n            }\n            setStudents([]);\n            setLoading(false);\n        }\n    };\n    // Helper function to determine year from branch (you can customize this logic)\n    const getYearFromBranch = (branch, student)=>{\n        if (student && student.joining_year && student.passout_year) {\n            return \"\".concat(student.joining_year, \"-\").concat(student.passout_year);\n        }\n        return 'N/A';\n    };\n    // Initial data fetch\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            fetchStudents();\n        }\n    }[\"StudentManagement.useEffect\"], []);\n    // Add this useEffect after your existing useEffect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            // Check if user is authenticated\n            const token = (0,_utils_auth__WEBPACK_IMPORTED_MODULE_3__.getAuthToken)();\n            if (!token) {\n                // Redirect to login page or show login prompt\n                setError('Please login to access student management.');\n                setLoading(false);\n                return;\n            }\n            fetchStudents();\n        }\n    }[\"StudentManagement.useEffect\"], []);\n    // Helper function to extract year from student ID (assuming format like CS2021001)\n    const getYearFromStudentId = (studentId)=>{\n        if (studentId && studentId.length >= 6) {\n            const yearPart = studentId.substring(2, 6);\n            if (!isNaN(yearPart)) {\n                return \"\".concat(4 - (new Date().getFullYear() - parseInt(yearPart)), \"th Year\");\n            }\n        }\n        return 'Unknown';\n    };\n    // Close dropdown when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"StudentManagement.useEffect.handleClickOutside\": (event)=>{\n                    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                        setIsYearDropdownOpen(false);\n                    }\n                }\n            }[\"StudentManagement.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"StudentManagement.useEffect\": ()=>{\n                    document.removeEventListener('mousedown', handleClickOutside);\n                }\n            })[\"StudentManagement.useEffect\"];\n        }\n    }[\"StudentManagement.useEffect\"], []);\n    // Get available years from students data\n    const getAvailableYears = (studentsData)=>{\n        const years = [\n            ...new Set(studentsData.map((student)=>student.year).filter((year)=>year && year !== 'N/A'))\n        ];\n        return years.sort();\n    };\n    // Get department statistics\n    const getDepartmentStats = (studentsData)=>{\n        const stats = {};\n        studentsData.forEach((student)=>{\n            if (student.department && student.department !== 'N/A') {\n                stats[student.department] = (stats[student.department] || 0) + 1;\n            }\n        });\n        return Object.entries(stats).map((param)=>{\n            let [department, count] = param;\n            return {\n                department,\n                count\n            };\n        });\n    };\n    // Get available passout years for selected department\n    const getAvailablePassoutYears = ()=>{\n        if (!selectedDepartment) return [];\n        const years = allStudents.filter((s)=>s.department === selectedDepartment && s.year && s.year !== 'N/A').map((s)=>{\n            // Extract passout year from year string (format: \"joining-passout\")\n            const parts = s.year.split('-');\n            return parts.length === 2 ? parts[1] : null;\n        }).filter((y)=>y).map(Number).filter((y)=>!isNaN(y));\n        // Unique and descending\n        return Array.from(new Set(years)).sort((a, b)=>b - a);\n    };\n    // Filter students for selected department and passout year\n    const getFilteredStudents = ()=>{\n        let filtered = allStudents;\n        if (selectedDepartment) {\n            filtered = filtered.filter((s)=>s.department === selectedDepartment);\n        }\n        if (selectedPassoutYear) {\n            filtered = filtered.filter((s)=>{\n                if (!s.year || s.year === 'N/A') return false;\n                const parts = s.year.split('-');\n                return parts.length === 2 && parts[1] === String(selectedPassoutYear);\n            });\n        }\n        if (debouncedSearchTerm) {\n            const searchLower = debouncedSearchTerm.toLowerCase();\n            filtered = filtered.filter((student)=>student.name.toLowerCase().includes(searchLower) || student.rollNumber.toLowerCase().includes(searchLower));\n        }\n        // CGPA filter\n        filtered = filtered.filter((student)=>{\n            const cgpa = parseFloat(student.cgpa);\n            if (cgpaMin && (isNaN(cgpa) || cgpa < parseFloat(cgpaMin))) return false;\n            if (cgpaMax && (isNaN(cgpa) || cgpa > parseFloat(cgpaMax))) return false;\n            return true;\n        });\n        return filtered;\n    };\n    // Update filters and refetch when dependencies change (but not searchTerm)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            if (allStudents.length > 0) {\n                fetchStudents(1); // Reset to page 1 when filters change\n            }\n        }\n    }[\"StudentManagement.useEffect\"], [\n        selectedDepartment,\n        selectedYear,\n        debouncedSearchTerm,\n        selectedPassoutYear\n    ]);\n    // Filter students based on selected department, year, and search term\n    const filteredStudents = students; // Students are already filtered in fetchStudents\n    const handleStudentClick = (student)=>{\n        setSelectedStudent(student);\n        setEditedStudent({\n            ...student\n        });\n        setIsEditing(false);\n    };\n    const handleBackToList = ()=>{\n        setSelectedStudent(null);\n        setIsEditing(false);\n        setEditedStudent(null);\n    };\n    const handleBackToDepartments = ()=>{\n        setSelectedDepartment(null);\n        setSelectedYear('all');\n        setSearchTerm('');\n    };\n    const handleEdit = ()=>{\n        setIsEditing(true);\n    };\n    const handleSave = async ()=>{\n        try {\n            setLoading(true);\n            // Helper function to clean data\n            const cleanValue = (value)=>{\n                if (value === '' || value === null || value === undefined) {\n                    return null;\n                }\n                // Handle string values\n                if (typeof value === 'string') {\n                    const trimmed = value.trim();\n                    return trimmed === '' ? null : trimmed;\n                }\n                return value;\n            };\n            // Helper function to clean numeric values\n            const cleanNumericValue = (value)=>{\n                if (value === '' || value === null || value === undefined) {\n                    return null;\n                }\n                if (typeof value === 'string') {\n                    const trimmed = value.trim();\n                    if (trimmed === '') return null;\n                    const parsed = parseInt(trimmed);\n                    return isNaN(parsed) ? null : parsed;\n                }\n                if (typeof value === 'number') {\n                    return isNaN(value) ? null : value;\n                }\n                return null;\n            };\n            // Helper function to clean string values specifically\n            const cleanStringValue = (value)=>{\n                if (value === '' || value === null || value === undefined) {\n                    return '';\n                }\n                return typeof value === 'string' ? value.trim() : String(value).trim();\n            };\n            // Split the name properly\n            const nameParts = editedStudent.name ? editedStudent.name.trim().split(' ') : [];\n            const firstName = nameParts[0] || '';\n            const lastName = nameParts.slice(1).join(' ') || '';\n            // Prepare the data for backend update\n            const updateData = {\n                // Basic information - ensure strings are not empty\n                first_name: cleanStringValue(firstName),\n                last_name: cleanStringValue(lastName),\n                student_id: cleanStringValue(editedStudent.rollNumber),\n                contact_email: cleanValue(editedStudent.email),\n                phone: cleanStringValue(editedStudent.phone),\n                branch: cleanStringValue(editedStudent.department),\n                gpa: cleanStringValue(editedStudent.gpa),\n                // Academic details - these should be integers\n                joining_year: cleanNumericValue(editedStudent.joining_year),\n                passout_year: cleanNumericValue(editedStudent.passout_year),\n                // Personal details\n                date_of_birth: cleanValue(editedStudent.dateOfBirth),\n                address: cleanStringValue(editedStudent.address),\n                city: cleanStringValue(editedStudent.city),\n                district: cleanStringValue(editedStudent.district),\n                state: cleanStringValue(editedStudent.state),\n                pincode: cleanStringValue(editedStudent.pincode),\n                country: cleanStringValue(editedStudent.country),\n                parent_contact: cleanStringValue(editedStudent.parentContact),\n                education: cleanStringValue(editedStudent.education),\n                skills: Array.isArray(editedStudent.skills) ? editedStudent.skills.filter((skill)=>skill && skill.trim()).join(', ') : cleanStringValue(editedStudent.skills),\n                // Academic scores - all as strings to match model\n                tenth_cgpa: cleanStringValue(editedStudent.tenth_cgpa),\n                tenth_percentage: cleanStringValue(editedStudent.tenth_percentage),\n                tenth_board: cleanStringValue(editedStudent.tenth_board),\n                tenth_school: cleanStringValue(editedStudent.tenth_school),\n                tenth_year_of_passing: cleanStringValue(editedStudent.tenth_year_of_passing),\n                tenth_location: cleanStringValue(editedStudent.tenth_location),\n                tenth_specialization: cleanStringValue(editedStudent.tenth_specialization),\n                twelfth_cgpa: cleanStringValue(editedStudent.twelfth_cgpa),\n                twelfth_percentage: cleanStringValue(editedStudent.twelfth_percentage),\n                twelfth_board: cleanStringValue(editedStudent.twelfth_board),\n                twelfth_school: cleanStringValue(editedStudent.twelfth_school),\n                twelfth_year_of_passing: cleanStringValue(editedStudent.twelfth_year_of_passing),\n                twelfth_location: cleanStringValue(editedStudent.twelfth_location),\n                twelfth_specialization: cleanStringValue(editedStudent.twelfth_specialization)\n            };\n            // Add semester CGPAs if they exist\n            if (editedStudent.semester_cgpas && Array.isArray(editedStudent.semester_cgpas)) {\n                editedStudent.semester_cgpas.forEach((semesterData)=>{\n                    if (semesterData.semester >= 1 && semesterData.semester <= 8 && semesterData.cgpa) {\n                        updateData[\"semester\".concat(semesterData.semester, \"_cgpa\")] = cleanStringValue(semesterData.cgpa);\n                    }\n                });\n            }\n            // Remove empty string values but keep nulls for proper field clearing\n            const cleanedUpdateData = Object.fromEntries(Object.entries(updateData).filter((param)=>{\n                let [key, value] = param;\n                // Keep nulls for clearing fields, remove empty strings except for required fields\n                const requiredFields = [\n                    'first_name',\n                    'last_name',\n                    'student_id',\n                    'gpa'\n                ];\n                if (requiredFields.includes(key)) {\n                    return value !== null && value !== undefined;\n                }\n                return value !== null && value !== undefined && value !== '';\n            }));\n            // Ensure required fields have default values if missing\n            if (!cleanedUpdateData.first_name) cleanedUpdateData.first_name = 'Student';\n            if (!cleanedUpdateData.last_name) cleanedUpdateData.last_name = '';\n            if (!cleanedUpdateData.student_id) cleanedUpdateData.student_id = \"TEMP_\".concat(Date.now());\n            if (!cleanedUpdateData.gpa) cleanedUpdateData.gpa = '0.0';\n            // Debug logging\n            console.log('Original editedStudent:', editedStudent);\n            console.log('Update data being sent:', cleanedUpdateData);\n            console.log('Student ID:', editedStudent.id);\n            // Make API call to update student\n            const updatedStudent = await _api_optimized__WEBPACK_IMPORTED_MODULE_2__.studentsAPI.updateStudent(editedStudent.id, cleanedUpdateData);\n            // Update the student in the list with the response data\n            const updatedStudentData = {\n                ...editedStudent,\n                ...updatedStudent,\n                name: \"\".concat(updatedStudent.first_name || '', \" \").concat(updatedStudent.last_name || '').trim(),\n                rollNumber: updatedStudent.student_id,\n                email: updatedStudent.contact_email,\n                department: updatedStudent.branch,\n                gpa: updatedStudent.gpa\n            };\n            setStudents((prev)=>prev.map((student)=>student.id === editedStudent.id ? updatedStudentData : student));\n            setSelectedStudent(updatedStudentData);\n            setIsEditing(false);\n            // Show success message\n            alert('Student profile updated successfully!');\n        } catch (error) {\n            var _error_response;\n            console.error('Error updating student:', error);\n            // More detailed error logging\n            if (error.response) {\n                console.error('Error response status:', error.response.status);\n                console.error('Error response data:', error.response.data);\n                console.error('Error response headers:', error.response.headers);\n            }\n            let errorMessage = 'Failed to update student profile. Please try again.';\n            if ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) {\n                // Handle validation errors\n                if (typeof error.response.data === 'object') {\n                    const errorDetails = [];\n                    for (const [field, messages] of Object.entries(error.response.data)){\n                        if (Array.isArray(messages)) {\n                            errorDetails.push(\"\".concat(field, \": \").concat(messages.join(', ')));\n                        } else {\n                            errorDetails.push(\"\".concat(field, \": \").concat(messages));\n                        }\n                    }\n                    if (errorDetails.length > 0) {\n                        errorMessage = \"Validation errors:\\n\".concat(errorDetails.join('\\n'));\n                    }\n                } else if (error.response.data.detail) {\n                    errorMessage = error.response.data.detail;\n                } else if (error.response.data.message) {\n                    errorMessage = error.response.data.message;\n                }\n            }\n            alert(\"Update failed: \".concat(errorMessage));\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleCancel = ()=>{\n        setEditedStudent({\n            ...selectedStudent\n        });\n        setIsEditing(false);\n    };\n    const handleInputChange = (field, value)=>{\n        setEditedStudent((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    // Handle retry button click\n    const handleRetry = ()=>{\n        setIsRetrying(true);\n        fetchStudents();\n    };\n    // Help developers find the correct API endpoint\n    const debugBackend = ()=>{\n        window.open('http://localhost:8000/admin/');\n    };\n    const handleSearch = ()=>{\n        // Force immediate search without waiting for debounce\n        setDebouncedSearchTerm(searchTerm);\n        setCurrentPage(1);\n    };\n    // Handle pagination\n    const handlePageChange = (newPage)=>{\n        if (newPage >= 1 && newPage <= totalPages) {\n            fetchStudents(newPage);\n        }\n    };\n    // Handle search input change\n    const handleSearchInputChange = (e)=>{\n        setSearchTerm(e.target.value);\n    // Don't trigger immediate search, let debounce handle it\n    };\n    // Handle search input key press\n    const handleSearchKeyDown = (e)=>{\n        if (e.key === 'Enter') {\n            e.preventDefault();\n            handleSearch();\n        }\n    };\n    if (loading) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 p-6 ml-20 overflow-y-auto h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center h-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mb-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                    lineNumber: 592,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-gray-600\",\n                    children: \"Loading students...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                    lineNumber: 593,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n            lineNumber: 591,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n        lineNumber: 590,\n        columnNumber: 5\n    }, this);\n    if (error && students.length === 0) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 p-6 ml-20 overflow-y-auto h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center h-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-red-500 mb-4 text-center max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-semibold text-lg mb-2\",\n                            children: \"Access Error\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 602,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 603,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 text-sm text-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Possible solutions:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                    lineNumber: 605,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"list-disc list-inside mt-2 text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Make sure you're logged in with admin credentials\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                            lineNumber: 607,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Check if your session has expired\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                            lineNumber: 608,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Verify Django server is running on port 8000\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                            lineNumber: 609,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Ensure proper permissions are set in Django\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                            lineNumber: 610,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                    lineNumber: 606,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 604,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                    lineNumber: 601,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-3 mt-4\",\n                    children: [\n                        !error.includes('login') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleRetry,\n                            className: \"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                            disabled: isRetrying,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_GraduationCap_RefreshCw_Save_Search_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-4 h-4 \".concat(isRetrying ? 'animate-spin' : '')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                    lineNumber: 621,\n                                    columnNumber: 15\n                                }, this),\n                                isRetrying ? 'Retrying...' : 'Retry'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 616,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>window.location.href = '/login',\n                            className: \"flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_GraduationCap_RefreshCw_Save_Search_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                    lineNumber: 629,\n                                    columnNumber: 13\n                                }, this),\n                                \"Go to Login\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 625,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                    lineNumber: 614,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n            lineNumber: 600,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n        lineNumber: 599,\n        columnNumber: 5\n    }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 p-6 ml-20 overflow-y-auto h-full\",\n        children: !selectedStudent ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: !selectedDepartment ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DepartmentCards__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                departmentOptions: departmentOptions,\n                departmentStats: departmentStats,\n                allStudents: allStudents,\n                onSelect: setSelectedDepartment\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                lineNumber: 642,\n                columnNumber: 13\n            }, this) : !selectedPassoutYear ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PassoutYearCards__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                departmentLabel: (_departmentOptions_find = departmentOptions.find((d)=>d.value === selectedDepartment)) === null || _departmentOptions_find === void 0 ? void 0 : _departmentOptions_find.label,\n                onBack: handleBackToDepartments,\n                getAvailablePassoutYears: getAvailablePassoutYears,\n                allStudents: allStudents,\n                selectedDepartment: selectedDepartment,\n                onSelectYear: setSelectedPassoutYear\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                lineNumber: 649,\n                columnNumber: 13\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StudentList__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                departmentLabel: (_departmentOptions_find1 = departmentOptions.find((d)=>d.value === selectedDepartment)) === null || _departmentOptions_find1 === void 0 ? void 0 : _departmentOptions_find1.label,\n                passoutYear: selectedPassoutYear,\n                onBack: ()=>setSelectedPassoutYear(null),\n                searchTerm: searchTerm,\n                handleSearchInputChange: handleSearchInputChange,\n                handleSearchKeyDown: handleSearchKeyDown,\n                cgpaMin: cgpaMin,\n                setCgpaMin: setCgpaMin,\n                cgpaMax: cgpaMax,\n                setCgpaMax: setCgpaMax,\n                handleSearch: handleSearch,\n                getFilteredStudents: getFilteredStudents,\n                currentPage: currentPage,\n                handlePageChange: handlePageChange,\n                handleStudentClick: handleStudentClick,\n                loading: loading\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                lineNumber: 658,\n                columnNumber: 13\n            }, this)\n        }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StudentProfile__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            selectedStudent: selectedStudent,\n            editedStudent: editedStudent,\n            isEditing: isEditing,\n            handleBackToList: handleBackToList,\n            handleEdit: handleEdit,\n            handleSave: handleSave,\n            handleCancel: handleCancel,\n            handleInputChange: handleInputChange,\n            departmentOptions: departmentOptions\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n            lineNumber: 679,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n        lineNumber: 638,\n        columnNumber: 5\n    }, this);\n}\n_s(StudentManagement, \"84yS5p8/FAIhW7pAdcz1mPZ+49Q=\");\n_c = StudentManagement;\nvar _c;\n$RefreshReg$(_c, \"StudentManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/student-management/page.jsx\n"));

/***/ })

});