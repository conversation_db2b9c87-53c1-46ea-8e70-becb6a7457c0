"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/companymanagement/page",{

/***/ "(app-pages-browser)/./src/app/admin/companymanagement/page.jsx":
/*!**************************************************!*\
  !*** ./src/app/admin/companymanagement/page.jsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Employers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Edit,Filter,GraduationCap,Heart,Loader,MapPin,PlusCircle,Search,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Edit,Filter,GraduationCap,Heart,Loader,MapPin,PlusCircle,Search,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Edit,Filter,GraduationCap,Heart,Loader,MapPin,PlusCircle,Search,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Edit,Filter,GraduationCap,Heart,Loader,MapPin,PlusCircle,Search,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Edit,Filter,GraduationCap,Heart,Loader,MapPin,PlusCircle,Search,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Edit,Filter,GraduationCap,Heart,Loader,MapPin,PlusCircle,Search,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Edit,Filter,GraduationCap,Heart,Loader,MapPin,PlusCircle,Search,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Edit,Filter,GraduationCap,Heart,Loader,MapPin,PlusCircle,Search,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Edit,Filter,GraduationCap,Heart,Loader,MapPin,PlusCircle,Search,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Edit,Filter,GraduationCap,Heart,Loader,MapPin,PlusCircle,Search,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Edit,Filter,GraduationCap,Heart,Loader,MapPin,PlusCircle,Search,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _api_optimized__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../api/optimized */ \"(app-pages-browser)/./src/api/optimized.js\");\n/* harmony import */ var _api_companies__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../api/companies */ \"(app-pages-browser)/./src/api/companies.js\");\n/* harmony import */ var _utils_auth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../utils/auth */ \"(app-pages-browser)/./src/utils/auth.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n// Import API services\n\n\n\nfunction Employers() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [companies, setCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [tierFilter, setTierFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('ALL');\n    const [industryFilter, setIndustryFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('ALL');\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('name');\n    const [followedCompanies, setFollowedCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        total: 0,\n        tier1: 0,\n        tier2: 0,\n        tier3: 0,\n        campus_recruiting: 0\n    });\n    // Pagination state\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalCount, setTotalCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(8); // Show 8 cards per page\n    // Fetch companies with server-side pagination and filtering\n    const fetchCompanies = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1;\n        try {\n            setLoading(true);\n            setError(null);\n            // Build filter parameters for server-side filtering\n            const params = {\n                page,\n                page_size: pageSize\n            };\n            // Add search filter\n            if (searchTerm) {\n                params.search = searchTerm;\n            }\n            // Add tier filter\n            if (tierFilter !== 'ALL') {\n                params.tier = tierFilter;\n            }\n            // Add industry filter\n            if (industryFilter !== 'ALL') {\n                params.industry = industryFilter;\n            }\n            // Add sorting\n            if (sortBy) {\n                params.ordering = sortBy === 'name' ? 'name' : sortBy === 'jobs' ? '-total_active_jobs' : sortBy === 'applicants' ? '-total_applicants' : sortBy === 'tier' ? 'tier' : 'name';\n            }\n            // Fetch data from optimized API\n            const response = await _api_optimized__WEBPACK_IMPORTED_MODULE_3__.companiesAPI.getCompanies(params);\n            setCompanies(response.data);\n            setCurrentPage(page);\n            setTotalPages(response.pagination.total_pages);\n            setTotalCount(response.pagination.total_count);\n            setLoading(false);\n        } catch (err) {\n            console.error('Error fetching companies:', err);\n            setError('Failed to load companies. Please try again.');\n            setCompanies([]);\n            setLoading(false);\n        }\n    };\n    // Fetch company statistics\n    const fetchStats = async ()=>{\n        try {\n            const statsResponse = await _api_optimized__WEBPACK_IMPORTED_MODULE_3__.companiesAPI.getCompanyStats();\n            setStats(statsResponse.data || statsResponse);\n        } catch (statsError) {\n            console.error('Error fetching company stats:', statsError);\n            // Fallback stats will be calculated from current page data\n            setStats({\n                total: totalCount,\n                tier1: 0,\n                tier2: 0,\n                tier3: 0,\n                campus_recruiting: 0\n            });\n        }\n    };\n    // Initial data fetch\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Employers.useEffect\": ()=>{\n            fetchCompanies();\n            fetchStats();\n            // Load followed companies from API\n            const userId = (0,_utils_auth__WEBPACK_IMPORTED_MODULE_5__.getUserId)();\n            if (userId) {\n                loadFollowedCompanies(userId);\n            }\n        }\n    }[\"Employers.useEffect\"], []);\n    // Function to load followed companies from API\n    const loadFollowedCompanies = async (userId)=>{\n        try {\n            const response = await (0,_api_companies__WEBPACK_IMPORTED_MODULE_4__.getUserFollowedCompanies)(userId);\n            const followedIds = response.data.map((company)=>company.id);\n            setFollowedCompanies(new Set(followedIds));\n        } catch (error) {\n            console.error('Error loading followed companies:', error);\n        }\n    };\n    // Industries will be fetched from backend or hardcoded for now\n    const industries = [\n        'Technology',\n        'Finance',\n        'Healthcare',\n        'Manufacturing',\n        'Consulting',\n        'E-commerce'\n    ];\n    // Companies are already filtered and sorted on the server side\n    const currentCompanies = companies;\n    // Change page\n    const paginate = (pageNumber)=>fetchCompanies(pageNumber);\n    const nextPage = ()=>{\n        if (currentPage < totalPages) {\n            fetchCompanies(currentPage + 1);\n        }\n    };\n    const prevPage = ()=>{\n        if (currentPage > 1) {\n            fetchCompanies(currentPage - 1);\n        }\n    };\n    // Refetch data when filters change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Employers.useEffect\": ()=>{\n            fetchCompanies(1); // Reset to page 1 when filters change\n        }\n    }[\"Employers.useEffect\"], [\n        searchTerm,\n        tierFilter,\n        industryFilter,\n        sortBy,\n        pageSize\n    ]);\n    const toggleFollowCompany = async (e, companyId)=>{\n        e.stopPropagation(); // Prevent navigation when clicking follow button\n        try {\n            const userId = (0,_utils_auth__WEBPACK_IMPORTED_MODULE_5__.getUserId)();\n            if (!userId) {\n                alert(\"Please log in to follow companies\");\n                return;\n            }\n            const newFollowedCompanies = new Set(followedCompanies);\n            if (newFollowedCompanies.has(companyId)) {\n                await unfollowCompany(companyId, userId);\n                newFollowedCompanies.delete(companyId);\n            } else {\n                await followCompany(companyId, userId);\n                newFollowedCompanies.add(companyId);\n            }\n            setFollowedCompanies(newFollowedCompanies);\n        } catch (error) {\n            console.error('Error updating follow status:', error);\n            alert('Failed to update follow status. Please try again.');\n        }\n    };\n    const getTierColor = (tier)=>{\n        switch(tier){\n            case 'Tier 1':\n                return 'bg-emerald-100 text-emerald-800 border-emerald-200';\n            case 'Tier 2':\n                return 'bg-blue-100 text-blue-800 border-blue-200';\n            case 'Tier 3':\n                return 'bg-purple-100 text-purple-800 border-purple-200';\n            default:\n                return 'bg-gray-100 text-gray-800 border-gray-200';\n        }\n    };\n    const handleCompanyClick = (companyId)=>{\n        router.push(\"/admin/companymanagement/\".concat(companyId));\n    };\n    const handleCreateCompany = ()=>{\n        router.push('/admin/companymanagement/create');\n    };\n    const handleEditCompany = (e, companyId)=>{\n        e.stopPropagation(); // Prevent navigation to company detail\n        router.push(\"/admin/companymanagement/edit/\".concat(companyId));\n    };\n    const handleDeleteCompany = async (e, companyId)=>{\n        e.stopPropagation(); // Prevent navigation to company detail\n        if (confirm('Are you sure you want to delete this company? This action cannot be undone.')) {\n            try {\n                await (0,_api_companies__WEBPACK_IMPORTED_MODULE_4__.deleteCompany)(companyId);\n                // Refresh the current page after deletion\n                fetchCompanies(currentPage);\n                alert('Company deleted successfully');\n            } catch (error) {\n                console.error('Error deleting company:', error);\n                alert('Failed to delete company. Please try again.');\n            }\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full flex flex-col items-center justify-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative w-16 h-16 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 border-t-4 border-blue-500 rounded-full animate-spin\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                            lineNumber: 244,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-2 border-r-4 border-transparent rounded-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                            lineNumber: 245,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                    lineNumber: 243,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 mb-6\",\n                    children: \"Loading companies...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                    lineNumber: 247,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n            lineNumber: 242,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center max-w-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-500 mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-16 h-16 mx-auto\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                            lineNumber: 257,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                        lineNumber: 256,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-semibold text-gray-900 mb-2\",\n                        children: \"Error Loading Companies\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                        lineNumber: 259,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-4\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                        lineNumber: 260,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>window.location.reload(),\n                        className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                        lineNumber: 261,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                lineNumber: 255,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n            lineNumber: 254,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full overflow-y-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6 flex justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                    children: \"Company Management\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleCreateCompany,\n                                    className: \"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors shadow-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Create New Company\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                            lineNumber: 277,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-5 gap-4 mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg p-4 shadow-sm border border-gray-100\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-blue-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-5 h-5 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: stats.total\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Total Companies\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 297,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg p-4 shadow-sm border border-gray-100\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-emerald-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-5 h-5 text-emerald-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: stats.tier1\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 308,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Tier 1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 309,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg p-4 shadow-sm border border-gray-100\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-blue-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-5 h-5 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: stats.tier2\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Tier 2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg p-4 shadow-sm border border-gray-100\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-amber-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-5 h-5 text-amber-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: stats.tier3\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Tier 3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 333,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg p-4 shadow-sm border border-gray-100\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-amber-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-5 h-5 text-amber-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: followedCompanies.size\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 344,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Following\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                            lineNumber: 289,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg p-6 shadow-sm border border-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col md:flex-row gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"Search companies, industries...\",\n                                                    value: searchTerm,\n                                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                                    className: \"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 357,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: tierFilter,\n                                                    onChange: (e)=>setTierFilter(e.target.value),\n                                                    className: \"px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[120px]\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"ALL\",\n                                                            children: \"All Tiers\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                            lineNumber: 373,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"Tier 1\",\n                                                            children: \"Tier 1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                            lineNumber: 374,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"Tier 2\",\n                                                            children: \"Tier 2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                            lineNumber: 375,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"Tier 3\",\n                                                            children: \"Tier 3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: industryFilter,\n                                                    onChange: (e)=>setIndustryFilter(e.target.value),\n                                                    className: \"px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[150px]\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"ALL\",\n                                                            children: \"All Industries\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                            lineNumber: 384,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        industries.map((industry)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: industry,\n                                                                children: industry\n                                                            }, industry, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                                lineNumber: 386,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: sortBy,\n                                                    onChange: (e)=>setSortBy(e.target.value),\n                                                    className: \"px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[130px]\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"name\",\n                                                            children: \"Company A-Z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                            lineNumber: 395,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"jobs\",\n                                                            children: \"Most Jobs\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                            lineNumber: 396,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"applicants\",\n                                                            children: \"Most Popular\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                            lineNumber: 397,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"tier\",\n                                                            children: \"Tier\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                            lineNumber: 398,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 390,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 text-sm text-gray-600\",\n                                    children: [\n                                        \"Showing \",\n                                        companies.length,\n                                        \" of \",\n                                        totalCount,\n                                        \" companies (Page \",\n                                        currentPage,\n                                        \" of \",\n                                        totalPages,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 403,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                            lineNumber: 352,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                    lineNumber: 276,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                    children: currentCompanies.length > 0 ? currentCompanies.map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            onClick: ()=>handleCompanyClick(company.id),\n                            className: \"bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-lg transition-all duration-200 cursor-pointer group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-bold text-lg\",\n                                                        children: company.name.charAt(0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 422,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-gray-900 group-hover:text-blue-600 transition-colors\",\n                                                            children: company.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                            lineNumber: 427,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: company.industry\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                            lineNumber: 430,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 426,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>handleEditCompany(e, company.id),\n                                                    className: \"p-2 rounded-lg transition-colors bg-blue-100 text-blue-600 hover:bg-blue-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 438,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>handleDeleteCompany(e, company.id),\n                                                    className: \"p-2 rounded-lg transition-colors bg-red-100 text-red-600 hover:bg-red-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 440,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 419,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 text-sm text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 452,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: company.location\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 451,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 text-sm text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 456,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: company.size\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 455,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 text-sm text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 460,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: company.founded ? \"Founded \".concat(company.founded) : 'Founded year not specified'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 450,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 py-1 rounded-full text-xs font-medium border \".concat(getTierColor(company.tier)),\n                                            children: company.tier\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 19\n                                        }, this),\n                                        company.campus_recruiting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-700 border border-green-200\",\n                                            children: \"Campus Recruiting\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 473,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 468,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 mb-4 line-clamp-3\",\n                                    children: company.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 480,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-3 gap-4 pt-4 border-t border-gray-100\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg font-bold text-gray-900\",\n                                                    children: company.totalActiveJobs\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 487,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: \"Active Jobs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 488,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 486,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg font-bold text-gray-900\",\n                                                    children: company.totalApplicants\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 491,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: \"Applicants\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 492,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 490,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg font-bold text-gray-900\",\n                                                    children: company.totalHired\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 495,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: \"Hired\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 496,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 494,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 485,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, company.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                            lineNumber: 413,\n                            columnNumber: 15\n                        }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-full flex flex-col items-center justify-center py-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-12 h-12 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 504,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                lineNumber: 503,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-2\",\n                                children: \"No companies found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                lineNumber: 506,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-4\",\n                                children: \"Try adjusting your search or filters\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                lineNumber: 507,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setSearchTerm('');\n                                    setTierFilter('ALL');\n                                    setIndustryFilter('ALL');\n                                    setSortBy('name');\n                                },\n                                className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                children: \"Clear Filters\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                lineNumber: 508,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                        lineNumber: 502,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                    lineNumber: 410,\n                    columnNumber: 9\n                }, this),\n                filteredCompanies.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 flex justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex items-center space-x-2\",\n                        \"aria-label\": \"Pagination\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: prevPage,\n                                disabled: currentPage === 1,\n                                className: \"px-4 py-2 rounded-md border \".concat(currentPage === 1 ? 'text-gray-400 bg-gray-100 cursor-not-allowed' : 'text-gray-700 hover:bg-gray-100'),\n                                children: \"Previous\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                lineNumber: 527,\n                                columnNumber: 15\n                            }, this),\n                            [\n                                ...Array(Math.min(5, totalPages))\n                            ].map((_, i)=>{\n                                // Logic to display pages around current page\n                                let pageNum;\n                                if (totalPages <= 5) {\n                                    pageNum = i + 1;\n                                } else if (currentPage <= 3) {\n                                    pageNum = i + 1;\n                                } else if (currentPage >= totalPages - 2) {\n                                    pageNum = totalPages - 4 + i;\n                                } else {\n                                    pageNum = currentPage - 2 + i;\n                                }\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>paginate(pageNum),\n                                    className: \"w-10 h-10 flex items-center justify-center rounded-md \".concat(currentPage === pageNum ? 'bg-blue-500 text-white' : 'text-gray-700 hover:bg-gray-100'),\n                                    \"aria-current\": currentPage === pageNum ? 'page' : undefined,\n                                    children: pageNum\n                                }, pageNum, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 552,\n                                    columnNumber: 19\n                                }, this);\n                            }),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: nextPage,\n                                disabled: currentPage === totalPages,\n                                className: \"px-4 py-2 rounded-md border \".concat(currentPage === totalPages ? 'text-gray-400 bg-gray-100 cursor-not-allowed' : 'text-gray-700 hover:bg-gray-100'),\n                                children: \"Next\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                lineNumber: 567,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                        lineNumber: 526,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                    lineNumber: 525,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n            lineNumber: 274,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n        lineNumber: 273,\n        columnNumber: 5\n    }, this);\n}\n_s(Employers, \"slan7mGoUv2bTmoFkjvdsGI+mQU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = Employers;\nvar _c;\n$RefreshReg$(_c, \"Employers\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/companymanagement/page.jsx\n"));

/***/ })

});