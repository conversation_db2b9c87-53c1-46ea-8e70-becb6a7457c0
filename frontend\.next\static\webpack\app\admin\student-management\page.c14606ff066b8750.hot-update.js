"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/student-management/page",{

/***/ "(app-pages-browser)/./src/app/admin/student-management/page.jsx":
/*!***************************************************!*\
  !*** ./src/app/admin/student-management/page.jsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StudentManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_GraduationCap_RefreshCw_Save_Search_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,GraduationCap,RefreshCw,Save,Search,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_GraduationCap_RefreshCw_Save_Search_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,GraduationCap,RefreshCw,Save,Search,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _api_optimized__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../api/optimized */ \"(app-pages-browser)/./src/api/optimized.js\");\n/* harmony import */ var _utils_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../utils/auth */ \"(app-pages-browser)/./src/utils/auth.js\");\n/* harmony import */ var _StudentDropdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./StudentDropdown */ \"(app-pages-browser)/./src/app/admin/student-management/StudentDropdown.jsx\");\n/* harmony import */ var _StudentProfile__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./StudentProfile */ \"(app-pages-browser)/./src/app/admin/student-management/StudentProfile.jsx\");\n/* harmony import */ var _DepartmentCards__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./DepartmentCards */ \"(app-pages-browser)/./src/app/admin/student-management/DepartmentCards.jsx\");\n/* harmony import */ var _PassoutYearCards__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./PassoutYearCards */ \"(app-pages-browser)/./src/app/admin/student-management/PassoutYearCards.jsx\");\n/* harmony import */ var _StudentList__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./StudentList */ \"(app-pages-browser)/./src/app/admin/student-management/StudentList.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction StudentManagement() {\n    var _departmentOptions_find, _departmentOptions_find1;\n    _s();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [debouncedSearchTerm, setDebouncedSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedDepartment, setSelectedDepartment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedYear, setSelectedYear] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [selectedStudent, setSelectedStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editedStudent, setEditedStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isYearDropdownOpen, setIsYearDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isRetrying, setIsRetrying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalStudents, setTotalStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(10);\n    const [availableYears, setAvailableYears] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [departmentStats, setDepartmentStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedPassoutYear, setSelectedPassoutYear] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [cgpaMin, setCgpaMin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [cgpaMax, setCgpaMax] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Dropdown options\n    const departmentOptions = [\n        {\n            value: 'Computer Science',\n            label: 'Computer Science'\n        },\n        {\n            value: 'Electronics',\n            label: 'Electronics'\n        },\n        {\n            value: 'Mechanical',\n            label: 'Mechanical'\n        },\n        {\n            value: 'Civil',\n            label: 'Civil'\n        },\n        {\n            value: 'Electrical',\n            label: 'Electrical'\n        },\n        {\n            value: 'Information Technology',\n            label: 'Information Technology'\n        },\n        {\n            value: 'Chemical',\n            label: 'Chemical'\n        },\n        {\n            value: 'Biotechnology',\n            label: 'Biotechnology'\n        }\n    ];\n    // Transform student data from API response\n    const transformStudentData = (student)=>({\n            id: student.id,\n            rollNumber: student.student_id || 'N/A',\n            name: \"\".concat(student.first_name || '', \" \").concat(student.last_name || '').trim() || 'Unknown',\n            email: student.contact_email || student.email || 'N/A',\n            phone: student.phone || 'N/A',\n            department: student.branch || 'N/A',\n            year: getYearFromBranch(student.branch, student),\n            cgpa: student.gpa || 'N/A',\n            gpa: student.gpa || 'N/A',\n            address: student.address || 'N/A',\n            dateOfBirth: student.date_of_birth || '',\n            parentContact: student.parent_contact || 'N/A',\n            education: student.education || 'N/A',\n            skills: student.skills || [],\n            // Academic details\n            joining_year: student.joining_year || student.admission_year || '',\n            passout_year: student.passout_year || student.graduation_year || '',\n            // Class XII details\n            twelfth_cgpa: student.twelfth_cgpa || student.class_12_cgpa || '',\n            twelfth_percentage: student.twelfth_percentage || student.class_12_percentage || '',\n            twelfth_year_of_passing: student.twelfth_year_of_passing || student.class_12_year || '',\n            twelfth_school: student.twelfth_school || student.class_12_school || '',\n            twelfth_board: student.twelfth_board || student.class_12_board || '',\n            twelfth_location: student.twelfth_location || student.class_12_location || '',\n            twelfth_specialization: student.twelfth_specialization || student.class_12_stream || '',\n            // Class X details\n            tenth_cgpa: student.tenth_cgpa || student.class_10_cgpa || '',\n            tenth_percentage: student.tenth_percentage || student.class_10_percentage || '',\n            tenth_year_of_passing: student.tenth_year_of_passing || student.class_10_year || '',\n            tenth_school: student.tenth_school || student.class_10_school || '',\n            tenth_board: student.tenth_board || student.class_10_board || '',\n            tenth_location: student.tenth_location || student.class_10_location || '',\n            tenth_specialization: student.tenth_specialization || student.class_10_stream || '',\n            // Address details\n            city: student.city || '',\n            district: student.district || '',\n            state: student.state || '',\n            pincode: student.pincode || student.pin_code || '',\n            country: student.country || 'India',\n            // Certificate URLs\n            tenth_certificate: student.tenth_certificate || student.class_10_certificate || '',\n            twelfth_certificate: student.twelfth_certificate || student.class_12_certificate || '',\n            tenth_certificate_url: student.tenth_certificate_url || student.class_10_certificate_url || '',\n            twelfth_certificate_url: student.twelfth_certificate_url || student.class_12_certificate_url || '',\n            // Resume details\n            resume: student.resume || '',\n            resume_url: student.resume_url || '',\n            // Semester-wise CGPA data - use actual backend data\n            semester_cgpas: student.semester_marksheets || [],\n            semester_marksheets: student.semester_marksheets || []\n        });\n    // Debounce search term\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"StudentManagement.useEffect.timer\": ()=>{\n                    setDebouncedSearchTerm(searchTerm);\n                }\n            }[\"StudentManagement.useEffect.timer\"], 300); // 300ms delay\n            return ({\n                \"StudentManagement.useEffect\": ()=>clearTimeout(timer)\n            })[\"StudentManagement.useEffect\"];\n        }\n    }[\"StudentManagement.useEffect\"], [\n        searchTerm\n    ]);\n    // Fetch students with server-side pagination and filtering\n    const fetchStudents = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1;\n        try {\n            setLoading(true);\n            setError(null);\n            setIsRetrying(false);\n            const token = (0,_utils_auth__WEBPACK_IMPORTED_MODULE_3__.getAuthToken)();\n            if (!token) {\n                throw new Error('No authentication token found. Please login first.');\n            }\n            // Build filter parameters for server-side filtering\n            const params = {\n                page,\n                page_size: pageSize\n            };\n            // Add search filter\n            if (debouncedSearchTerm) {\n                params.search = debouncedSearchTerm;\n            }\n            // Add department filter\n            if (selectedDepartment) {\n                params.department = selectedDepartment;\n            }\n            // Add year filter (convert to passout year if needed)\n            if (selectedYear !== 'all') {\n                params.year = selectedYear;\n            }\n            // Add CGPA filters\n            if (cgpaMin) {\n                params.cgpa_min = cgpaMin;\n            }\n            if (cgpaMax) {\n                params.cgpa_max = cgpaMax;\n            }\n            // Fetch data from optimized API\n            const response = await _api_optimized__WEBPACK_IMPORTED_MODULE_2__.studentsAPI.getStudents(params);\n            // Transform the data\n            const transformedStudents = response.data.map(transformStudentData);\n            setStudents(transformedStudents);\n            setCurrentPage(page);\n            setTotalPages(response.pagination.total_pages);\n            setTotalStudents(response.pagination.total_count);\n            setLoading(false);\n        } catch (err) {\n            var _err_response, _err_response1;\n            console.error('Error fetching students:', err);\n            if (((_err_response = err.response) === null || _err_response === void 0 ? void 0 : _err_response.status) === 401) {\n                setError('Authentication failed. Please login again.');\n            } else if (((_err_response1 = err.response) === null || _err_response1 === void 0 ? void 0 : _err_response1.status) === 403) {\n                setError('You do not have permission to view students. Admin access required.');\n            } else if (err.message.includes('token')) {\n                setError('Please login to access student management.');\n            } else {\n                setError(\"Error: \".concat(err.message));\n            }\n            setStudents([]);\n            setLoading(false);\n        }\n    };\n    // Helper function to determine year from branch (you can customize this logic)\n    const getYearFromBranch = (branch, student)=>{\n        if (student && student.joining_year && student.passout_year) {\n            return \"\".concat(student.joining_year, \"-\").concat(student.passout_year);\n        }\n        return 'N/A';\n    };\n    // Initial data fetch\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            fetchStudents();\n        }\n    }[\"StudentManagement.useEffect\"], []);\n    // Add this useEffect after your existing useEffect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            // Check if user is authenticated\n            const token = (0,_utils_auth__WEBPACK_IMPORTED_MODULE_3__.getAuthToken)();\n            if (!token) {\n                // Redirect to login page or show login prompt\n                setError('Please login to access student management.');\n                setLoading(false);\n                return;\n            }\n            fetchStudents();\n        }\n    }[\"StudentManagement.useEffect\"], []);\n    // Helper function to extract year from student ID (assuming format like CS2021001)\n    const getYearFromStudentId = (studentId)=>{\n        if (studentId && studentId.length >= 6) {\n            const yearPart = studentId.substring(2, 6);\n            if (!isNaN(yearPart)) {\n                return \"\".concat(4 - (new Date().getFullYear() - parseInt(yearPart)), \"th Year\");\n            }\n        }\n        return 'Unknown';\n    };\n    // Close dropdown when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"StudentManagement.useEffect.handleClickOutside\": (event)=>{\n                    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                        setIsYearDropdownOpen(false);\n                    }\n                }\n            }[\"StudentManagement.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"StudentManagement.useEffect\": ()=>{\n                    document.removeEventListener('mousedown', handleClickOutside);\n                }\n            })[\"StudentManagement.useEffect\"];\n        }\n    }[\"StudentManagement.useEffect\"], []);\n    // Get available years from students data\n    const getAvailableYears = (studentsData)=>{\n        const years = [\n            ...new Set(studentsData.map((student)=>student.year).filter((year)=>year && year !== 'N/A'))\n        ];\n        return years.sort();\n    };\n    // Get department statistics\n    const getDepartmentStats = (studentsData)=>{\n        const stats = {};\n        studentsData.forEach((student)=>{\n            if (student.department && student.department !== 'N/A') {\n                stats[student.department] = (stats[student.department] || 0) + 1;\n            }\n        });\n        return Object.entries(stats).map((param)=>{\n            let [department, count] = param;\n            return {\n                department,\n                count\n            };\n        });\n    };\n    // Get available passout years for selected department\n    const getAvailablePassoutYears = ()=>{\n        if (!selectedDepartment) return [];\n        const years = allStudents.filter((s)=>s.department === selectedDepartment && s.year && s.year !== 'N/A').map((s)=>{\n            // Extract passout year from year string (format: \"joining-passout\")\n            const parts = s.year.split('-');\n            return parts.length === 2 ? parts[1] : null;\n        }).filter((y)=>y).map(Number).filter((y)=>!isNaN(y));\n        // Unique and descending\n        return Array.from(new Set(years)).sort((a, b)=>b - a);\n    };\n    // Filter students for selected department and passout year\n    const getFilteredStudents = ()=>{\n        let filtered = allStudents;\n        if (selectedDepartment) {\n            filtered = filtered.filter((s)=>s.department === selectedDepartment);\n        }\n        if (selectedPassoutYear) {\n            filtered = filtered.filter((s)=>{\n                if (!s.year || s.year === 'N/A') return false;\n                const parts = s.year.split('-');\n                return parts.length === 2 && parts[1] === String(selectedPassoutYear);\n            });\n        }\n        if (debouncedSearchTerm) {\n            const searchLower = debouncedSearchTerm.toLowerCase();\n            filtered = filtered.filter((student)=>student.name.toLowerCase().includes(searchLower) || student.rollNumber.toLowerCase().includes(searchLower));\n        }\n        // CGPA filter\n        filtered = filtered.filter((student)=>{\n            const cgpa = parseFloat(student.cgpa);\n            if (cgpaMin && (isNaN(cgpa) || cgpa < parseFloat(cgpaMin))) return false;\n            if (cgpaMax && (isNaN(cgpa) || cgpa > parseFloat(cgpaMax))) return false;\n            return true;\n        });\n        return filtered;\n    };\n    // Update filters and refetch when dependencies change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            fetchStudents(1); // Reset to page 1 when filters change\n        }\n    }[\"StudentManagement.useEffect\"], [\n        selectedDepartment,\n        selectedYear,\n        debouncedSearchTerm,\n        selectedPassoutYear,\n        cgpaMin,\n        cgpaMax,\n        pageSize\n    ]);\n    // Filter students based on selected department, year, and search term\n    const filteredStudents = students; // Students are already filtered in fetchStudents\n    const handleStudentClick = (student)=>{\n        setSelectedStudent(student);\n        setEditedStudent({\n            ...student\n        });\n        setIsEditing(false);\n    };\n    const handleBackToList = ()=>{\n        setSelectedStudent(null);\n        setIsEditing(false);\n        setEditedStudent(null);\n    };\n    const handleBackToDepartments = ()=>{\n        setSelectedDepartment(null);\n        setSelectedYear('all');\n        setSearchTerm('');\n    };\n    const handleEdit = ()=>{\n        setIsEditing(true);\n    };\n    const handleSave = async ()=>{\n        try {\n            setLoading(true);\n            // Helper function to clean data\n            const cleanValue = (value)=>{\n                if (value === '' || value === null || value === undefined) {\n                    return null;\n                }\n                // Handle string values\n                if (typeof value === 'string') {\n                    const trimmed = value.trim();\n                    return trimmed === '' ? null : trimmed;\n                }\n                return value;\n            };\n            // Helper function to clean numeric values\n            const cleanNumericValue = (value)=>{\n                if (value === '' || value === null || value === undefined) {\n                    return null;\n                }\n                if (typeof value === 'string') {\n                    const trimmed = value.trim();\n                    if (trimmed === '') return null;\n                    const parsed = parseInt(trimmed);\n                    return isNaN(parsed) ? null : parsed;\n                }\n                if (typeof value === 'number') {\n                    return isNaN(value) ? null : value;\n                }\n                return null;\n            };\n            // Helper function to clean string values specifically\n            const cleanStringValue = (value)=>{\n                if (value === '' || value === null || value === undefined) {\n                    return '';\n                }\n                return typeof value === 'string' ? value.trim() : String(value).trim();\n            };\n            // Split the name properly\n            const nameParts = editedStudent.name ? editedStudent.name.trim().split(' ') : [];\n            const firstName = nameParts[0] || '';\n            const lastName = nameParts.slice(1).join(' ') || '';\n            // Prepare the data for backend update\n            const updateData = {\n                // Basic information - ensure strings are not empty\n                first_name: cleanStringValue(firstName),\n                last_name: cleanStringValue(lastName),\n                student_id: cleanStringValue(editedStudent.rollNumber),\n                contact_email: cleanValue(editedStudent.email),\n                phone: cleanStringValue(editedStudent.phone),\n                branch: cleanStringValue(editedStudent.department),\n                gpa: cleanStringValue(editedStudent.gpa),\n                // Academic details - these should be integers\n                joining_year: cleanNumericValue(editedStudent.joining_year),\n                passout_year: cleanNumericValue(editedStudent.passout_year),\n                // Personal details\n                date_of_birth: cleanValue(editedStudent.dateOfBirth),\n                address: cleanStringValue(editedStudent.address),\n                city: cleanStringValue(editedStudent.city),\n                district: cleanStringValue(editedStudent.district),\n                state: cleanStringValue(editedStudent.state),\n                pincode: cleanStringValue(editedStudent.pincode),\n                country: cleanStringValue(editedStudent.country),\n                parent_contact: cleanStringValue(editedStudent.parentContact),\n                education: cleanStringValue(editedStudent.education),\n                skills: Array.isArray(editedStudent.skills) ? editedStudent.skills.filter((skill)=>skill && skill.trim()).join(', ') : cleanStringValue(editedStudent.skills),\n                // Academic scores - all as strings to match model\n                tenth_cgpa: cleanStringValue(editedStudent.tenth_cgpa),\n                tenth_percentage: cleanStringValue(editedStudent.tenth_percentage),\n                tenth_board: cleanStringValue(editedStudent.tenth_board),\n                tenth_school: cleanStringValue(editedStudent.tenth_school),\n                tenth_year_of_passing: cleanStringValue(editedStudent.tenth_year_of_passing),\n                tenth_location: cleanStringValue(editedStudent.tenth_location),\n                tenth_specialization: cleanStringValue(editedStudent.tenth_specialization),\n                twelfth_cgpa: cleanStringValue(editedStudent.twelfth_cgpa),\n                twelfth_percentage: cleanStringValue(editedStudent.twelfth_percentage),\n                twelfth_board: cleanStringValue(editedStudent.twelfth_board),\n                twelfth_school: cleanStringValue(editedStudent.twelfth_school),\n                twelfth_year_of_passing: cleanStringValue(editedStudent.twelfth_year_of_passing),\n                twelfth_location: cleanStringValue(editedStudent.twelfth_location),\n                twelfth_specialization: cleanStringValue(editedStudent.twelfth_specialization)\n            };\n            // Add semester CGPAs if they exist\n            if (editedStudent.semester_cgpas && Array.isArray(editedStudent.semester_cgpas)) {\n                editedStudent.semester_cgpas.forEach((semesterData)=>{\n                    if (semesterData.semester >= 1 && semesterData.semester <= 8 && semesterData.cgpa) {\n                        updateData[\"semester\".concat(semesterData.semester, \"_cgpa\")] = cleanStringValue(semesterData.cgpa);\n                    }\n                });\n            }\n            // Remove empty string values but keep nulls for proper field clearing\n            const cleanedUpdateData = Object.fromEntries(Object.entries(updateData).filter((param)=>{\n                let [key, value] = param;\n                // Keep nulls for clearing fields, remove empty strings except for required fields\n                const requiredFields = [\n                    'first_name',\n                    'last_name',\n                    'student_id',\n                    'gpa'\n                ];\n                if (requiredFields.includes(key)) {\n                    return value !== null && value !== undefined;\n                }\n                return value !== null && value !== undefined && value !== '';\n            }));\n            // Ensure required fields have default values if missing\n            if (!cleanedUpdateData.first_name) cleanedUpdateData.first_name = 'Student';\n            if (!cleanedUpdateData.last_name) cleanedUpdateData.last_name = '';\n            if (!cleanedUpdateData.student_id) cleanedUpdateData.student_id = \"TEMP_\".concat(Date.now());\n            if (!cleanedUpdateData.gpa) cleanedUpdateData.gpa = '0.0';\n            // Debug logging\n            console.log('Original editedStudent:', editedStudent);\n            console.log('Update data being sent:', cleanedUpdateData);\n            console.log('Student ID:', editedStudent.id);\n            // Make API call to update student\n            const updatedStudent = await _api_optimized__WEBPACK_IMPORTED_MODULE_2__.studentsAPI.updateStudent(editedStudent.id, cleanedUpdateData);\n            // Update the student in the list with the response data\n            const updatedStudentData = {\n                ...editedStudent,\n                ...updatedStudent,\n                name: \"\".concat(updatedStudent.first_name || '', \" \").concat(updatedStudent.last_name || '').trim(),\n                rollNumber: updatedStudent.student_id,\n                email: updatedStudent.contact_email,\n                department: updatedStudent.branch,\n                gpa: updatedStudent.gpa\n            };\n            setStudents((prev)=>prev.map((student)=>student.id === editedStudent.id ? updatedStudentData : student));\n            setSelectedStudent(updatedStudentData);\n            setIsEditing(false);\n            // Show success message\n            alert('Student profile updated successfully!');\n        } catch (error) {\n            var _error_response;\n            console.error('Error updating student:', error);\n            // More detailed error logging\n            if (error.response) {\n                console.error('Error response status:', error.response.status);\n                console.error('Error response data:', error.response.data);\n                console.error('Error response headers:', error.response.headers);\n            }\n            let errorMessage = 'Failed to update student profile. Please try again.';\n            if ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) {\n                // Handle validation errors\n                if (typeof error.response.data === 'object') {\n                    const errorDetails = [];\n                    for (const [field, messages] of Object.entries(error.response.data)){\n                        if (Array.isArray(messages)) {\n                            errorDetails.push(\"\".concat(field, \": \").concat(messages.join(', ')));\n                        } else {\n                            errorDetails.push(\"\".concat(field, \": \").concat(messages));\n                        }\n                    }\n                    if (errorDetails.length > 0) {\n                        errorMessage = \"Validation errors:\\n\".concat(errorDetails.join('\\n'));\n                    }\n                } else if (error.response.data.detail) {\n                    errorMessage = error.response.data.detail;\n                } else if (error.response.data.message) {\n                    errorMessage = error.response.data.message;\n                }\n            }\n            alert(\"Update failed: \".concat(errorMessage));\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleCancel = ()=>{\n        setEditedStudent({\n            ...selectedStudent\n        });\n        setIsEditing(false);\n    };\n    const handleInputChange = (field, value)=>{\n        setEditedStudent((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    // Handle retry button click\n    const handleRetry = ()=>{\n        setIsRetrying(true);\n        fetchStudents();\n    };\n    // Help developers find the correct API endpoint\n    const debugBackend = ()=>{\n        window.open('http://localhost:8000/admin/');\n    };\n    const handleSearch = ()=>{\n        // Force immediate search without waiting for debounce\n        setDebouncedSearchTerm(searchTerm);\n        setCurrentPage(1);\n    };\n    // Handle pagination\n    const handlePageChange = (newPage)=>{\n        if (newPage >= 1 && newPage <= totalPages) {\n            fetchStudents(newPage);\n        }\n    };\n    // Handle search input change\n    const handleSearchInputChange = (e)=>{\n        setSearchTerm(e.target.value);\n    // Don't trigger immediate search, let debounce handle it\n    };\n    // Handle search input key press\n    const handleSearchKeyDown = (e)=>{\n        if (e.key === 'Enter') {\n            e.preventDefault();\n            handleSearch();\n        }\n    };\n    if (loading) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 p-6 ml-20 overflow-y-auto h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center h-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mb-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                    lineNumber: 589,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-gray-600\",\n                    children: \"Loading students...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                    lineNumber: 590,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n            lineNumber: 588,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n        lineNumber: 587,\n        columnNumber: 5\n    }, this);\n    if (error && students.length === 0) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 p-6 ml-20 overflow-y-auto h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center h-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-red-500 mb-4 text-center max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-semibold text-lg mb-2\",\n                            children: \"Access Error\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 599,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 600,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 text-sm text-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Possible solutions:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                    lineNumber: 602,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"list-disc list-inside mt-2 text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Make sure you're logged in with admin credentials\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                            lineNumber: 604,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Check if your session has expired\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                            lineNumber: 605,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Verify Django server is running on port 8000\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                            lineNumber: 606,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Ensure proper permissions are set in Django\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                            lineNumber: 607,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                    lineNumber: 603,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 601,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                    lineNumber: 598,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-3 mt-4\",\n                    children: [\n                        !error.includes('login') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleRetry,\n                            className: \"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                            disabled: isRetrying,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_GraduationCap_RefreshCw_Save_Search_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-4 h-4 \".concat(isRetrying ? 'animate-spin' : '')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                    lineNumber: 618,\n                                    columnNumber: 15\n                                }, this),\n                                isRetrying ? 'Retrying...' : 'Retry'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 613,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>window.location.href = '/login',\n                            className: \"flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_GraduationCap_RefreshCw_Save_Search_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                    lineNumber: 626,\n                                    columnNumber: 13\n                                }, this),\n                                \"Go to Login\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 622,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                    lineNumber: 611,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n            lineNumber: 597,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n        lineNumber: 596,\n        columnNumber: 5\n    }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 p-6 ml-20 overflow-y-auto h-full\",\n        children: !selectedStudent ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: !selectedDepartment ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DepartmentCards__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                departmentOptions: departmentOptions,\n                departmentStats: departmentStats,\n                allStudents: allStudents,\n                onSelect: setSelectedDepartment\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                lineNumber: 639,\n                columnNumber: 13\n            }, this) : !selectedPassoutYear ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PassoutYearCards__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                departmentLabel: (_departmentOptions_find = departmentOptions.find((d)=>d.value === selectedDepartment)) === null || _departmentOptions_find === void 0 ? void 0 : _departmentOptions_find.label,\n                onBack: handleBackToDepartments,\n                getAvailablePassoutYears: getAvailablePassoutYears,\n                allStudents: allStudents,\n                selectedDepartment: selectedDepartment,\n                onSelectYear: setSelectedPassoutYear\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                lineNumber: 646,\n                columnNumber: 13\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StudentList__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                departmentLabel: (_departmentOptions_find1 = departmentOptions.find((d)=>d.value === selectedDepartment)) === null || _departmentOptions_find1 === void 0 ? void 0 : _departmentOptions_find1.label,\n                passoutYear: selectedPassoutYear,\n                onBack: ()=>setSelectedPassoutYear(null),\n                searchTerm: searchTerm,\n                handleSearchInputChange: handleSearchInputChange,\n                handleSearchKeyDown: handleSearchKeyDown,\n                cgpaMin: cgpaMin,\n                setCgpaMin: setCgpaMin,\n                cgpaMax: cgpaMax,\n                setCgpaMax: setCgpaMax,\n                handleSearch: handleSearch,\n                getFilteredStudents: getFilteredStudents,\n                currentPage: currentPage,\n                handlePageChange: handlePageChange,\n                handleStudentClick: handleStudentClick,\n                loading: loading\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                lineNumber: 655,\n                columnNumber: 13\n            }, this)\n        }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StudentProfile__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            selectedStudent: selectedStudent,\n            editedStudent: editedStudent,\n            isEditing: isEditing,\n            handleBackToList: handleBackToList,\n            handleEdit: handleEdit,\n            handleSave: handleSave,\n            handleCancel: handleCancel,\n            handleInputChange: handleInputChange,\n            departmentOptions: departmentOptions\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n            lineNumber: 676,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n        lineNumber: 635,\n        columnNumber: 5\n    }, this);\n}\n_s(StudentManagement, \"MByQbfljhdtahv1cKIsASH8Inzo=\");\n_c = StudentManagement;\nvar _c;\n$RefreshReg$(_c, \"StudentManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/student-management/page.jsx\n"));

/***/ })

});