"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/student-management/page",{

/***/ "(app-pages-browser)/./src/app/admin/student-management/page.jsx":
/*!***************************************************!*\
  !*** ./src/app/admin/student-management/page.jsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StudentManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_GraduationCap_RefreshCw_Save_Search_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,GraduationCap,RefreshCw,Save,Search,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_GraduationCap_RefreshCw_Save_Search_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,GraduationCap,RefreshCw,Save,Search,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _api_optimized__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../api/optimized */ \"(app-pages-browser)/./src/api/optimized.js\");\n/* harmony import */ var _utils_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../utils/auth */ \"(app-pages-browser)/./src/utils/auth.js\");\n/* harmony import */ var _StudentDropdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./StudentDropdown */ \"(app-pages-browser)/./src/app/admin/student-management/StudentDropdown.jsx\");\n/* harmony import */ var _StudentProfile__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./StudentProfile */ \"(app-pages-browser)/./src/app/admin/student-management/StudentProfile.jsx\");\n/* harmony import */ var _DepartmentCards__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./DepartmentCards */ \"(app-pages-browser)/./src/app/admin/student-management/DepartmentCards.jsx\");\n/* harmony import */ var _PassoutYearCards__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./PassoutYearCards */ \"(app-pages-browser)/./src/app/admin/student-management/PassoutYearCards.jsx\");\n/* harmony import */ var _StudentList__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./StudentList */ \"(app-pages-browser)/./src/app/admin/student-management/StudentList.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction StudentManagement() {\n    var _departmentOptions_find, _departmentOptions_find1;\n    _s();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [debouncedSearchTerm, setDebouncedSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedDepartment, setSelectedDepartment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedYear, setSelectedYear] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [selectedStudent, setSelectedStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editedStudent, setEditedStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isYearDropdownOpen, setIsYearDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allStudents, setAllStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isRetrying, setIsRetrying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalStudents, setTotalStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(10);\n    const [availableYears, setAvailableYears] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [departmentStats, setDepartmentStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedPassoutYear, setSelectedPassoutYear] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [cgpaMin, setCgpaMin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [cgpaMax, setCgpaMax] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Dropdown options\n    const departmentOptions = [\n        {\n            value: 'Computer Science',\n            label: 'Computer Science'\n        },\n        {\n            value: 'Electronics',\n            label: 'Electronics'\n        },\n        {\n            value: 'Mechanical',\n            label: 'Mechanical'\n        },\n        {\n            value: 'Civil',\n            label: 'Civil'\n        },\n        {\n            value: 'Electrical',\n            label: 'Electrical'\n        },\n        {\n            value: 'Information Technology',\n            label: 'Information Technology'\n        },\n        {\n            value: 'Chemical',\n            label: 'Chemical'\n        },\n        {\n            value: 'Biotechnology',\n            label: 'Biotechnology'\n        }\n    ];\n    // Transform student data from API response\n    const transformStudentData = (student)=>({\n            id: student.id,\n            rollNumber: student.student_id || 'N/A',\n            name: \"\".concat(student.first_name || '', \" \").concat(student.last_name || '').trim() || 'Unknown',\n            email: student.contact_email || student.email || 'N/A',\n            phone: student.phone || 'N/A',\n            department: student.branch || 'N/A',\n            year: getYearFromBranch(student.branch, student),\n            cgpa: student.gpa || 'N/A',\n            gpa: student.gpa || 'N/A',\n            address: student.address || 'N/A',\n            dateOfBirth: student.date_of_birth || '',\n            parentContact: student.parent_contact || 'N/A',\n            education: student.education || 'N/A',\n            skills: student.skills || [],\n            // Academic details\n            joining_year: student.joining_year || student.admission_year || '',\n            passout_year: student.passout_year || student.graduation_year || '',\n            // Class XII details\n            twelfth_cgpa: student.twelfth_cgpa || student.class_12_cgpa || '',\n            twelfth_percentage: student.twelfth_percentage || student.class_12_percentage || '',\n            twelfth_year_of_passing: student.twelfth_year_of_passing || student.class_12_year || '',\n            twelfth_school: student.twelfth_school || student.class_12_school || '',\n            twelfth_board: student.twelfth_board || student.class_12_board || '',\n            twelfth_location: student.twelfth_location || student.class_12_location || '',\n            twelfth_specialization: student.twelfth_specialization || student.class_12_stream || '',\n            // Class X details\n            tenth_cgpa: student.tenth_cgpa || student.class_10_cgpa || '',\n            tenth_percentage: student.tenth_percentage || student.class_10_percentage || '',\n            tenth_year_of_passing: student.tenth_year_of_passing || student.class_10_year || '',\n            tenth_school: student.tenth_school || student.class_10_school || '',\n            tenth_board: student.tenth_board || student.class_10_board || '',\n            tenth_location: student.tenth_location || student.class_10_location || '',\n            tenth_specialization: student.tenth_specialization || student.class_10_stream || '',\n            // Address details\n            city: student.city || '',\n            district: student.district || '',\n            state: student.state || '',\n            pincode: student.pincode || student.pin_code || '',\n            country: student.country || 'India',\n            // Certificate URLs\n            tenth_certificate: student.tenth_certificate || student.class_10_certificate || '',\n            twelfth_certificate: student.twelfth_certificate || student.class_12_certificate || '',\n            tenth_certificate_url: student.tenth_certificate_url || student.class_10_certificate_url || '',\n            twelfth_certificate_url: student.twelfth_certificate_url || student.class_12_certificate_url || '',\n            // Resume details\n            resume: student.resume || '',\n            resume_url: student.resume_url || '',\n            // Semester-wise CGPA data - use actual backend data\n            semester_cgpas: student.semester_marksheets || [],\n            semester_marksheets: student.semester_marksheets || []\n        });\n    // Debounce search term\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"StudentManagement.useEffect.timer\": ()=>{\n                    setDebouncedSearchTerm(searchTerm);\n                }\n            }[\"StudentManagement.useEffect.timer\"], 300); // 300ms delay\n            return ({\n                \"StudentManagement.useEffect\": ()=>clearTimeout(timer)\n            })[\"StudentManagement.useEffect\"];\n        }\n    }[\"StudentManagement.useEffect\"], [\n        searchTerm\n    ]);\n    // Fetch students with server-side pagination and filtering\n    const fetchStudents = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1;\n        try {\n            setLoading(true);\n            setError(null);\n            setIsRetrying(false);\n            const token = (0,_utils_auth__WEBPACK_IMPORTED_MODULE_3__.getAuthToken)();\n            if (!token) {\n                throw new Error('No authentication token found. Please login first.');\n            }\n            // Build filter parameters for server-side filtering\n            const params = {\n                page,\n                page_size: pageSize\n            };\n            // Add search filter\n            if (debouncedSearchTerm) {\n                params.search = debouncedSearchTerm;\n            }\n            // Add department filter\n            if (selectedDepartment) {\n                params.department = selectedDepartment;\n            }\n            // Add year filter (convert to passout year if needed)\n            if (selectedYear !== 'all') {\n                params.year = selectedYear;\n            }\n            // Add CGPA filters\n            if (cgpaMin) {\n                params.cgpa_min = cgpaMin;\n            }\n            if (cgpaMax) {\n                params.cgpa_max = cgpaMax;\n            }\n            // Fetch data from optimized API\n            const response = await _api_optimized__WEBPACK_IMPORTED_MODULE_2__.studentsAPI.getStudents(params);\n            // Transform the data\n            const transformedStudents = response.data.map(transformStudentData);\n            setStudents(transformedStudents);\n            setCurrentPage(page);\n            setTotalPages(response.pagination.total_pages);\n            setTotalStudents(response.pagination.total_count);\n            setLoading(false);\n        } catch (err) {\n            var _err_response, _err_response1;\n            console.error('Error fetching students:', err);\n            if (((_err_response = err.response) === null || _err_response === void 0 ? void 0 : _err_response.status) === 401) {\n                setError('Authentication failed. Please login again.');\n            } else if (((_err_response1 = err.response) === null || _err_response1 === void 0 ? void 0 : _err_response1.status) === 403) {\n                setError('You do not have permission to view students. Admin access required.');\n            } else if (err.message.includes('token')) {\n                setError('Please login to access student management.');\n            } else {\n                setError(\"Error: \".concat(err.message));\n            }\n            setStudents([]);\n            setLoading(false);\n        }\n    };\n    // Helper function to determine year from branch (you can customize this logic)\n    const getYearFromBranch = (branch, student)=>{\n        if (student && student.joining_year && student.passout_year) {\n            return \"\".concat(student.joining_year, \"-\").concat(student.passout_year);\n        }\n        return 'N/A';\n    };\n    // Initial data fetch\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            fetchStudents();\n        }\n    }[\"StudentManagement.useEffect\"], []);\n    // Add this useEffect after your existing useEffect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            // Check if user is authenticated\n            const token = (0,_utils_auth__WEBPACK_IMPORTED_MODULE_3__.getAuthToken)();\n            if (!token) {\n                // Redirect to login page or show login prompt\n                setError('Please login to access student management.');\n                setLoading(false);\n                return;\n            }\n            fetchStudents();\n        }\n    }[\"StudentManagement.useEffect\"], []);\n    // Helper function to extract year from student ID (assuming format like CS2021001)\n    const getYearFromStudentId = (studentId)=>{\n        if (studentId && studentId.length >= 6) {\n            const yearPart = studentId.substring(2, 6);\n            if (!isNaN(yearPart)) {\n                return \"\".concat(4 - (new Date().getFullYear() - parseInt(yearPart)), \"th Year\");\n            }\n        }\n        return 'Unknown';\n    };\n    // Close dropdown when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"StudentManagement.useEffect.handleClickOutside\": (event)=>{\n                    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                        setIsYearDropdownOpen(false);\n                    }\n                }\n            }[\"StudentManagement.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"StudentManagement.useEffect\": ()=>{\n                    document.removeEventListener('mousedown', handleClickOutside);\n                }\n            })[\"StudentManagement.useEffect\"];\n        }\n    }[\"StudentManagement.useEffect\"], []);\n    // Get available years from students data\n    const getAvailableYears = (studentsData)=>{\n        const years = [\n            ...new Set(studentsData.map((student)=>student.year).filter((year)=>year && year !== 'N/A'))\n        ];\n        return years.sort();\n    };\n    // Get department statistics\n    const getDepartmentStats = (studentsData)=>{\n        const stats = {};\n        studentsData.forEach((student)=>{\n            if (student.department && student.department !== 'N/A') {\n                stats[student.department] = (stats[student.department] || 0) + 1;\n            }\n        });\n        return Object.entries(stats).map((param)=>{\n            let [department, count] = param;\n            return {\n                department,\n                count\n            };\n        });\n    };\n    // Get available passout years for selected department\n    const getAvailablePassoutYears = ()=>{\n        if (!selectedDepartment) return [];\n        const years = allStudents.filter((s)=>s.department === selectedDepartment && s.year && s.year !== 'N/A').map((s)=>{\n            // Extract passout year from year string (format: \"joining-passout\")\n            const parts = s.year.split('-');\n            return parts.length === 2 ? parts[1] : null;\n        }).filter((y)=>y).map(Number).filter((y)=>!isNaN(y));\n        // Unique and descending\n        return Array.from(new Set(years)).sort((a, b)=>b - a);\n    };\n    // Filter students for selected department and passout year\n    const getFilteredStudents = ()=>{\n        let filtered = allStudents;\n        if (selectedDepartment) {\n            filtered = filtered.filter((s)=>s.department === selectedDepartment);\n        }\n        if (selectedPassoutYear) {\n            filtered = filtered.filter((s)=>{\n                if (!s.year || s.year === 'N/A') return false;\n                const parts = s.year.split('-');\n                return parts.length === 2 && parts[1] === String(selectedPassoutYear);\n            });\n        }\n        if (debouncedSearchTerm) {\n            const searchLower = debouncedSearchTerm.toLowerCase();\n            filtered = filtered.filter((student)=>student.name.toLowerCase().includes(searchLower) || student.rollNumber.toLowerCase().includes(searchLower));\n        }\n        // CGPA filter\n        filtered = filtered.filter((student)=>{\n            const cgpa = parseFloat(student.cgpa);\n            if (cgpaMin && (isNaN(cgpa) || cgpa < parseFloat(cgpaMin))) return false;\n            if (cgpaMax && (isNaN(cgpa) || cgpa > parseFloat(cgpaMax))) return false;\n            return true;\n        });\n        return filtered;\n    };\n    // Update filters and refetch when dependencies change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            fetchStudents(1); // Reset to page 1 when filters change\n        }\n    }[\"StudentManagement.useEffect\"], [\n        selectedDepartment,\n        selectedYear,\n        debouncedSearchTerm,\n        selectedPassoutYear,\n        cgpaMin,\n        cgpaMax,\n        pageSize\n    ]);\n    // Filter students based on selected department, year, and search term\n    const filteredStudents = students; // Students are already filtered in fetchStudents\n    const handleStudentClick = (student)=>{\n        setSelectedStudent(student);\n        setEditedStudent({\n            ...student\n        });\n        setIsEditing(false);\n    };\n    const handleBackToList = ()=>{\n        setSelectedStudent(null);\n        setIsEditing(false);\n        setEditedStudent(null);\n    };\n    const handleBackToDepartments = ()=>{\n        setSelectedDepartment(null);\n        setSelectedYear('all');\n        setSearchTerm('');\n    };\n    const handleEdit = ()=>{\n        setIsEditing(true);\n    };\n    const handleSave = async ()=>{\n        try {\n            setLoading(true);\n            // Helper function to clean data\n            const cleanValue = (value)=>{\n                if (value === '' || value === null || value === undefined) {\n                    return null;\n                }\n                // Handle string values\n                if (typeof value === 'string') {\n                    const trimmed = value.trim();\n                    return trimmed === '' ? null : trimmed;\n                }\n                return value;\n            };\n            // Helper function to clean numeric values\n            const cleanNumericValue = (value)=>{\n                if (value === '' || value === null || value === undefined) {\n                    return null;\n                }\n                if (typeof value === 'string') {\n                    const trimmed = value.trim();\n                    if (trimmed === '') return null;\n                    const parsed = parseInt(trimmed);\n                    return isNaN(parsed) ? null : parsed;\n                }\n                if (typeof value === 'number') {\n                    return isNaN(value) ? null : value;\n                }\n                return null;\n            };\n            // Helper function to clean string values specifically\n            const cleanStringValue = (value)=>{\n                if (value === '' || value === null || value === undefined) {\n                    return '';\n                }\n                return typeof value === 'string' ? value.trim() : String(value).trim();\n            };\n            // Split the name properly\n            const nameParts = editedStudent.name ? editedStudent.name.trim().split(' ') : [];\n            const firstName = nameParts[0] || '';\n            const lastName = nameParts.slice(1).join(' ') || '';\n            // Prepare the data for backend update\n            const updateData = {\n                // Basic information - ensure strings are not empty\n                first_name: cleanStringValue(firstName),\n                last_name: cleanStringValue(lastName),\n                student_id: cleanStringValue(editedStudent.rollNumber),\n                contact_email: cleanValue(editedStudent.email),\n                phone: cleanStringValue(editedStudent.phone),\n                branch: cleanStringValue(editedStudent.department),\n                gpa: cleanStringValue(editedStudent.gpa),\n                // Academic details - these should be integers\n                joining_year: cleanNumericValue(editedStudent.joining_year),\n                passout_year: cleanNumericValue(editedStudent.passout_year),\n                // Personal details\n                date_of_birth: cleanValue(editedStudent.dateOfBirth),\n                address: cleanStringValue(editedStudent.address),\n                city: cleanStringValue(editedStudent.city),\n                district: cleanStringValue(editedStudent.district),\n                state: cleanStringValue(editedStudent.state),\n                pincode: cleanStringValue(editedStudent.pincode),\n                country: cleanStringValue(editedStudent.country),\n                parent_contact: cleanStringValue(editedStudent.parentContact),\n                education: cleanStringValue(editedStudent.education),\n                skills: Array.isArray(editedStudent.skills) ? editedStudent.skills.filter((skill)=>skill && skill.trim()).join(', ') : cleanStringValue(editedStudent.skills),\n                // Academic scores - all as strings to match model\n                tenth_cgpa: cleanStringValue(editedStudent.tenth_cgpa),\n                tenth_percentage: cleanStringValue(editedStudent.tenth_percentage),\n                tenth_board: cleanStringValue(editedStudent.tenth_board),\n                tenth_school: cleanStringValue(editedStudent.tenth_school),\n                tenth_year_of_passing: cleanStringValue(editedStudent.tenth_year_of_passing),\n                tenth_location: cleanStringValue(editedStudent.tenth_location),\n                tenth_specialization: cleanStringValue(editedStudent.tenth_specialization),\n                twelfth_cgpa: cleanStringValue(editedStudent.twelfth_cgpa),\n                twelfth_percentage: cleanStringValue(editedStudent.twelfth_percentage),\n                twelfth_board: cleanStringValue(editedStudent.twelfth_board),\n                twelfth_school: cleanStringValue(editedStudent.twelfth_school),\n                twelfth_year_of_passing: cleanStringValue(editedStudent.twelfth_year_of_passing),\n                twelfth_location: cleanStringValue(editedStudent.twelfth_location),\n                twelfth_specialization: cleanStringValue(editedStudent.twelfth_specialization)\n            };\n            // Add semester CGPAs if they exist\n            if (editedStudent.semester_cgpas && Array.isArray(editedStudent.semester_cgpas)) {\n                editedStudent.semester_cgpas.forEach((semesterData)=>{\n                    if (semesterData.semester >= 1 && semesterData.semester <= 8 && semesterData.cgpa) {\n                        updateData[\"semester\".concat(semesterData.semester, \"_cgpa\")] = cleanStringValue(semesterData.cgpa);\n                    }\n                });\n            }\n            // Remove empty string values but keep nulls for proper field clearing\n            const cleanedUpdateData = Object.fromEntries(Object.entries(updateData).filter((param)=>{\n                let [key, value] = param;\n                // Keep nulls for clearing fields, remove empty strings except for required fields\n                const requiredFields = [\n                    'first_name',\n                    'last_name',\n                    'student_id',\n                    'gpa'\n                ];\n                if (requiredFields.includes(key)) {\n                    return value !== null && value !== undefined;\n                }\n                return value !== null && value !== undefined && value !== '';\n            }));\n            // Ensure required fields have default values if missing\n            if (!cleanedUpdateData.first_name) cleanedUpdateData.first_name = 'Student';\n            if (!cleanedUpdateData.last_name) cleanedUpdateData.last_name = '';\n            if (!cleanedUpdateData.student_id) cleanedUpdateData.student_id = \"TEMP_\".concat(Date.now());\n            if (!cleanedUpdateData.gpa) cleanedUpdateData.gpa = '0.0';\n            // Debug logging\n            console.log('Original editedStudent:', editedStudent);\n            console.log('Update data being sent:', cleanedUpdateData);\n            console.log('Student ID:', editedStudent.id);\n            // Make API call to update student\n            const updatedStudent = await _api_optimized__WEBPACK_IMPORTED_MODULE_2__.studentsAPI.updateStudent(editedStudent.id, cleanedUpdateData);\n            // Update the student in the list with the response data\n            const updatedStudentData = {\n                ...editedStudent,\n                ...updatedStudent,\n                name: \"\".concat(updatedStudent.first_name || '', \" \").concat(updatedStudent.last_name || '').trim(),\n                rollNumber: updatedStudent.student_id,\n                email: updatedStudent.contact_email,\n                department: updatedStudent.branch,\n                gpa: updatedStudent.gpa\n            };\n            setStudents((prev)=>prev.map((student)=>student.id === editedStudent.id ? updatedStudentData : student));\n            setSelectedStudent(updatedStudentData);\n            setIsEditing(false);\n            // Show success message\n            alert('Student profile updated successfully!');\n        } catch (error) {\n            var _error_response;\n            console.error('Error updating student:', error);\n            // More detailed error logging\n            if (error.response) {\n                console.error('Error response status:', error.response.status);\n                console.error('Error response data:', error.response.data);\n                console.error('Error response headers:', error.response.headers);\n            }\n            let errorMessage = 'Failed to update student profile. Please try again.';\n            if ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) {\n                // Handle validation errors\n                if (typeof error.response.data === 'object') {\n                    const errorDetails = [];\n                    for (const [field, messages] of Object.entries(error.response.data)){\n                        if (Array.isArray(messages)) {\n                            errorDetails.push(\"\".concat(field, \": \").concat(messages.join(', ')));\n                        } else {\n                            errorDetails.push(\"\".concat(field, \": \").concat(messages));\n                        }\n                    }\n                    if (errorDetails.length > 0) {\n                        errorMessage = \"Validation errors:\\n\".concat(errorDetails.join('\\n'));\n                    }\n                } else if (error.response.data.detail) {\n                    errorMessage = error.response.data.detail;\n                } else if (error.response.data.message) {\n                    errorMessage = error.response.data.message;\n                }\n            }\n            alert(\"Update failed: \".concat(errorMessage));\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleCancel = ()=>{\n        setEditedStudent({\n            ...selectedStudent\n        });\n        setIsEditing(false);\n    };\n    const handleInputChange = (field, value)=>{\n        setEditedStudent((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    // Handle retry button click\n    const handleRetry = ()=>{\n        setIsRetrying(true);\n        fetchStudents();\n    };\n    // Help developers find the correct API endpoint\n    const debugBackend = ()=>{\n        window.open('http://localhost:8000/admin/');\n    };\n    const handleSearch = ()=>{\n        // Force immediate search without waiting for debounce\n        setDebouncedSearchTerm(searchTerm);\n        setCurrentPage(1);\n    };\n    // Handle pagination\n    const handlePageChange = (newPage)=>{\n        if (newPage >= 1 && newPage <= totalPages) {\n            fetchStudents(newPage);\n        }\n    };\n    // Handle search input change\n    const handleSearchInputChange = (e)=>{\n        setSearchTerm(e.target.value);\n    // Don't trigger immediate search, let debounce handle it\n    };\n    // Handle search input key press\n    const handleSearchKeyDown = (e)=>{\n        if (e.key === 'Enter') {\n            e.preventDefault();\n            handleSearch();\n        }\n    };\n    if (loading) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 p-6 ml-20 overflow-y-auto h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center h-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mb-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                    lineNumber: 590,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-gray-600\",\n                    children: \"Loading students...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                    lineNumber: 591,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n            lineNumber: 589,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n        lineNumber: 588,\n        columnNumber: 5\n    }, this);\n    if (error && students.length === 0) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 p-6 ml-20 overflow-y-auto h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center h-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-red-500 mb-4 text-center max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-semibold text-lg mb-2\",\n                            children: \"Access Error\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 600,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 601,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 text-sm text-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Possible solutions:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                    lineNumber: 603,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"list-disc list-inside mt-2 text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Make sure you're logged in with admin credentials\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                            lineNumber: 605,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Check if your session has expired\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                            lineNumber: 606,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Verify Django server is running on port 8000\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                            lineNumber: 607,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Ensure proper permissions are set in Django\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                            lineNumber: 608,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                    lineNumber: 604,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 602,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                    lineNumber: 599,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-3 mt-4\",\n                    children: [\n                        !error.includes('login') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleRetry,\n                            className: \"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                            disabled: isRetrying,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_GraduationCap_RefreshCw_Save_Search_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-4 h-4 \".concat(isRetrying ? 'animate-spin' : '')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                    lineNumber: 619,\n                                    columnNumber: 15\n                                }, this),\n                                isRetrying ? 'Retrying...' : 'Retry'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 614,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>window.location.href = '/login',\n                            className: \"flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_GraduationCap_RefreshCw_Save_Search_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                    lineNumber: 627,\n                                    columnNumber: 13\n                                }, this),\n                                \"Go to Login\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 623,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                    lineNumber: 612,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n            lineNumber: 598,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n        lineNumber: 597,\n        columnNumber: 5\n    }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 p-6 ml-20 overflow-y-auto h-full\",\n        children: !selectedStudent ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: !selectedDepartment ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DepartmentCards__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                departmentOptions: departmentOptions,\n                departmentStats: departmentStats,\n                allStudents: allStudents,\n                onSelect: setSelectedDepartment\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                lineNumber: 640,\n                columnNumber: 13\n            }, this) : !selectedPassoutYear ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PassoutYearCards__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                departmentLabel: (_departmentOptions_find = departmentOptions.find((d)=>d.value === selectedDepartment)) === null || _departmentOptions_find === void 0 ? void 0 : _departmentOptions_find.label,\n                onBack: handleBackToDepartments,\n                getAvailablePassoutYears: getAvailablePassoutYears,\n                allStudents: allStudents,\n                selectedDepartment: selectedDepartment,\n                onSelectYear: setSelectedPassoutYear\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                lineNumber: 647,\n                columnNumber: 13\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StudentList__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                departmentLabel: (_departmentOptions_find1 = departmentOptions.find((d)=>d.value === selectedDepartment)) === null || _departmentOptions_find1 === void 0 ? void 0 : _departmentOptions_find1.label,\n                passoutYear: selectedPassoutYear,\n                onBack: ()=>setSelectedPassoutYear(null),\n                searchTerm: searchTerm,\n                handleSearchInputChange: handleSearchInputChange,\n                handleSearchKeyDown: handleSearchKeyDown,\n                cgpaMin: cgpaMin,\n                setCgpaMin: setCgpaMin,\n                cgpaMax: cgpaMax,\n                setCgpaMax: setCgpaMax,\n                handleSearch: handleSearch,\n                getFilteredStudents: getFilteredStudents,\n                currentPage: currentPage,\n                handlePageChange: handlePageChange,\n                handleStudentClick: handleStudentClick,\n                loading: loading\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                lineNumber: 656,\n                columnNumber: 13\n            }, this)\n        }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StudentProfile__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            selectedStudent: selectedStudent,\n            editedStudent: editedStudent,\n            isEditing: isEditing,\n            handleBackToList: handleBackToList,\n            handleEdit: handleEdit,\n            handleSave: handleSave,\n            handleCancel: handleCancel,\n            handleInputChange: handleInputChange,\n            departmentOptions: departmentOptions\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n            lineNumber: 677,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n        lineNumber: 636,\n        columnNumber: 5\n    }, this);\n}\n_s(StudentManagement, \"84yS5p8/FAIhW7pAdcz1mPZ+49Q=\");\n_c = StudentManagement;\nvar _c;\n$RefreshReg$(_c, \"StudentManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvYWRtaW4vc3R1ZGVudC1tYW5hZ2VtZW50L3BhZ2UuanN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVdzQjtBQUM4QjtBQUNDO0FBQ0Y7QUFDSjtBQUNEO0FBQ0U7QUFDRTtBQUNWO0FBRXpCLFNBQVNrQjtRQWtuQk9DLHlCQVNBQTs7SUExbkI3QixNQUFNLENBQUNDLFlBQVlDLGNBQWMsR0FBR1gsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDWSxxQkFBcUJDLHVCQUF1QixHQUFHYiwrQ0FBUUEsQ0FBQztJQUMvRCxNQUFNLENBQUNjLG9CQUFvQkMsc0JBQXNCLEdBQUdmLCtDQUFRQSxDQUFDO0lBQzdELE1BQU0sQ0FBQ2dCLGNBQWNDLGdCQUFnQixHQUFHakIsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDa0IsaUJBQWlCQyxtQkFBbUIsR0FBR25CLCtDQUFRQSxDQUFDO0lBQ3ZELE1BQU0sQ0FBQ29CLFdBQVdDLGFBQWEsR0FBR3JCLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ3NCLGVBQWVDLGlCQUFpQixHQUFHdkIsK0NBQVFBLENBQUM7SUFDbkQsTUFBTXdCLGNBQWN6Qiw2Q0FBTUEsQ0FBQztJQUMzQixNQUFNLENBQUMwQixvQkFBb0JDLHNCQUFzQixHQUFHMUIsK0NBQVFBLENBQUM7SUFDN0QsTUFBTSxDQUFDMkIsVUFBVUMsWUFBWSxHQUFHNUIsK0NBQVFBLENBQUMsRUFBRTtJQUMzQyxNQUFNLENBQUM2QixhQUFhQyxlQUFlLEdBQUc5QiwrQ0FBUUEsQ0FBQyxFQUFFO0lBQ2pELE1BQU0sQ0FBQytCLFNBQVNDLFdBQVcsR0FBR2hDLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQ2lDLE9BQU9DLFNBQVMsR0FBR2xDLCtDQUFRQSxDQUFDO0lBQ25DLE1BQU0sQ0FBQ21DLFlBQVlDLGNBQWMsR0FBR3BDLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ3FDLGFBQWFDLGVBQWUsR0FBR3RDLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQ3VDLFlBQVlDLGNBQWMsR0FBR3hDLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ3lDLGVBQWVDLGlCQUFpQixHQUFHMUMsK0NBQVFBLENBQUM7SUFDbkQsTUFBTSxDQUFDMkMsVUFBVUMsWUFBWSxHQUFHNUMsK0NBQVFBLENBQUM7SUFDekMsTUFBTSxDQUFDNkMsZ0JBQWdCQyxrQkFBa0IsR0FBRzlDLCtDQUFRQSxDQUFDLEVBQUU7SUFDdkQsTUFBTSxDQUFDK0MsaUJBQWlCQyxtQkFBbUIsR0FBR2hELCtDQUFRQSxDQUFDLEVBQUU7SUFDekQsTUFBTSxDQUFDaUQscUJBQXFCQyx1QkFBdUIsR0FBR2xELCtDQUFRQSxDQUFDO0lBQy9ELE1BQU0sQ0FBQ21ELFNBQVNDLFdBQVcsR0FBR3BELCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQ3FELFNBQVNDLFdBQVcsR0FBR3RELCtDQUFRQSxDQUFDO0lBRXZDLG1CQUFtQjtJQUNuQixNQUFNUyxvQkFBb0I7UUFDeEI7WUFBRThDLE9BQU87WUFBb0JDLE9BQU87UUFBbUI7UUFDdkQ7WUFBRUQsT0FBTztZQUFlQyxPQUFPO1FBQWM7UUFDN0M7WUFBRUQsT0FBTztZQUFjQyxPQUFPO1FBQWE7UUFDM0M7WUFBRUQsT0FBTztZQUFTQyxPQUFPO1FBQVE7UUFDakM7WUFBRUQsT0FBTztZQUFjQyxPQUFPO1FBQWE7UUFDM0M7WUFBRUQsT0FBTztZQUEwQkMsT0FBTztRQUF5QjtRQUNuRTtZQUFFRCxPQUFPO1lBQVlDLE9BQU87UUFBVztRQUN2QztZQUFFRCxPQUFPO1lBQWlCQyxPQUFPO1FBQWdCO0tBQ2xEO0lBRUQsMkNBQTJDO0lBQzNDLE1BQU1DLHVCQUF1QixDQUFDQyxVQUFhO1lBQ3pDQyxJQUFJRCxRQUFRQyxFQUFFO1lBQ2RDLFlBQVlGLFFBQVFHLFVBQVUsSUFBSTtZQUNsQ0MsTUFBTSxHQUErQkosT0FBNUJBLFFBQVFLLFVBQVUsSUFBSSxJQUFHLEtBQTJCLE9BQXhCTCxRQUFRTSxTQUFTLElBQUksSUFBS0MsSUFBSSxNQUFNO1lBQ3pFQyxPQUFPUixRQUFRUyxhQUFhLElBQUlULFFBQVFRLEtBQUssSUFBSTtZQUNqREUsT0FBT1YsUUFBUVUsS0FBSyxJQUFJO1lBQ3hCQyxZQUFZWCxRQUFRWSxNQUFNLElBQUk7WUFDOUJDLE1BQU1DLGtCQUFrQmQsUUFBUVksTUFBTSxFQUFFWjtZQUN4Q2UsTUFBTWYsUUFBUWdCLEdBQUcsSUFBSTtZQUNyQkEsS0FBS2hCLFFBQVFnQixHQUFHLElBQUk7WUFDcEJDLFNBQVNqQixRQUFRaUIsT0FBTyxJQUFJO1lBQzVCQyxhQUFhbEIsUUFBUW1CLGFBQWEsSUFBSTtZQUN0Q0MsZUFBZXBCLFFBQVFxQixjQUFjLElBQUk7WUFDekNDLFdBQVd0QixRQUFRc0IsU0FBUyxJQUFJO1lBQ2hDQyxRQUFRdkIsUUFBUXVCLE1BQU0sSUFBSSxFQUFFO1lBRTVCLG1CQUFtQjtZQUNmQyxjQUFjeEIsUUFBUXdCLFlBQVksSUFBSXhCLFFBQVF5QixjQUFjLElBQUk7WUFDaEVDLGNBQWMxQixRQUFRMEIsWUFBWSxJQUFJMUIsUUFBUTJCLGVBQWUsSUFBSTtZQUVqRSxvQkFBb0I7WUFDcEJDLGNBQWM1QixRQUFRNEIsWUFBWSxJQUFJNUIsUUFBUTZCLGFBQWEsSUFBSTtZQUMvREMsb0JBQW9COUIsUUFBUThCLGtCQUFrQixJQUFJOUIsUUFBUStCLG1CQUFtQixJQUFJO1lBQ2pGQyx5QkFBeUJoQyxRQUFRZ0MsdUJBQXVCLElBQUloQyxRQUFRaUMsYUFBYSxJQUFJO1lBQ3JGQyxnQkFBZ0JsQyxRQUFRa0MsY0FBYyxJQUFJbEMsUUFBUW1DLGVBQWUsSUFBSTtZQUNyRUMsZUFBZXBDLFFBQVFvQyxhQUFhLElBQUlwQyxRQUFRcUMsY0FBYyxJQUFJO1lBQ2xFQyxrQkFBa0J0QyxRQUFRc0MsZ0JBQWdCLElBQUl0QyxRQUFRdUMsaUJBQWlCLElBQUk7WUFDM0VDLHdCQUF3QnhDLFFBQVF3QyxzQkFBc0IsSUFBSXhDLFFBQVF5QyxlQUFlLElBQUk7WUFFckYsa0JBQWtCO1lBQ2xCQyxZQUFZMUMsUUFBUTBDLFVBQVUsSUFBSTFDLFFBQVEyQyxhQUFhLElBQUk7WUFDM0RDLGtCQUFrQjVDLFFBQVE0QyxnQkFBZ0IsSUFBSTVDLFFBQVE2QyxtQkFBbUIsSUFBSTtZQUM3RUMsdUJBQXVCOUMsUUFBUThDLHFCQUFxQixJQUFJOUMsUUFBUStDLGFBQWEsSUFBSTtZQUNqRkMsY0FBY2hELFFBQVFnRCxZQUFZLElBQUloRCxRQUFRaUQsZUFBZSxJQUFJO1lBQ2pFQyxhQUFhbEQsUUFBUWtELFdBQVcsSUFBSWxELFFBQVFtRCxjQUFjLElBQUk7WUFDOURDLGdCQUFnQnBELFFBQVFvRCxjQUFjLElBQUlwRCxRQUFRcUQsaUJBQWlCLElBQUk7WUFDdkVDLHNCQUFzQnRELFFBQVFzRCxvQkFBb0IsSUFBSXRELFFBQVF1RCxlQUFlLElBQUk7WUFFakYsa0JBQWtCO1lBQ2xCQyxNQUFNeEQsUUFBUXdELElBQUksSUFBSTtZQUN0QkMsVUFBVXpELFFBQVF5RCxRQUFRLElBQUk7WUFDOUJDLE9BQU8xRCxRQUFRMEQsS0FBSyxJQUFJO1lBQ3hCQyxTQUFTM0QsUUFBUTJELE9BQU8sSUFBSTNELFFBQVE0RCxRQUFRLElBQUk7WUFDaERDLFNBQVM3RCxRQUFRNkQsT0FBTyxJQUFJO1lBRTVCLG1CQUFtQjtZQUNuQkMsbUJBQW1COUQsUUFBUThELGlCQUFpQixJQUFJOUQsUUFBUStELG9CQUFvQixJQUFJO1lBQ2hGQyxxQkFBcUJoRSxRQUFRZ0UsbUJBQW1CLElBQUloRSxRQUFRaUUsb0JBQW9CLElBQUk7WUFDcEZDLHVCQUF1QmxFLFFBQVFrRSxxQkFBcUIsSUFBSWxFLFFBQVFtRSx3QkFBd0IsSUFBSTtZQUM1RkMseUJBQXlCcEUsUUFBUW9FLHVCQUF1QixJQUFJcEUsUUFBUXFFLHdCQUF3QixJQUFJO1lBRWhHLGlCQUFpQjtZQUNqQkMsUUFBUXRFLFFBQVFzRSxNQUFNLElBQUk7WUFDMUJDLFlBQVl2RSxRQUFRdUUsVUFBVSxJQUFJO1lBRWxDLG9EQUFvRDtZQUNwREMsZ0JBQWdCeEUsUUFBUXlFLG1CQUFtQixJQUFJLEVBQUU7WUFDakRBLHFCQUFxQnpFLFFBQVF5RSxtQkFBbUIsSUFBSSxFQUFFO1FBQ3hEO0lBRUosdUJBQXVCO0lBQ3ZCckksZ0RBQVNBO3VDQUFDO1lBQ1IsTUFBTXNJLFFBQVFDO3FEQUFXO29CQUN2QnhILHVCQUF1Qkg7Z0JBQ3pCO29EQUFHLE1BQU0sY0FBYztZQUV2QjsrQ0FBTyxJQUFNNEgsYUFBYUY7O1FBQzVCO3NDQUFHO1FBQUMxSDtLQUFXO0lBRWYsMkRBQTJEO0lBQzNELE1BQU02SCxnQkFBZ0I7WUFBT0Msd0VBQU87UUFDbEMsSUFBSTtZQUNGeEcsV0FBVztZQUNYRSxTQUFTO1lBQ1RFLGNBQWM7WUFFZCxNQUFNcUcsUUFBUXZJLHlEQUFZQTtZQUMxQixJQUFJLENBQUN1SSxPQUFPO2dCQUNWLE1BQU0sSUFBSUMsTUFBTTtZQUNsQjtZQUVBLG9EQUFvRDtZQUNwRCxNQUFNQyxTQUFTO2dCQUNiSDtnQkFDQUksV0FBV2pHO1lBQ2I7WUFFQSxvQkFBb0I7WUFDcEIsSUFBSS9CLHFCQUFxQjtnQkFDdkIrSCxPQUFPRSxNQUFNLEdBQUdqSTtZQUNsQjtZQUVBLHdCQUF3QjtZQUN4QixJQUFJRSxvQkFBb0I7Z0JBQ3RCNkgsT0FBT3RFLFVBQVUsR0FBR3ZEO1lBQ3RCO1lBRUEsc0RBQXNEO1lBQ3RELElBQUlFLGlCQUFpQixPQUFPO2dCQUMxQjJILE9BQU9wRSxJQUFJLEdBQUd2RDtZQUNoQjtZQUVBLG1CQUFtQjtZQUNuQixJQUFJbUMsU0FBUztnQkFDWHdGLE9BQU9HLFFBQVEsR0FBRzNGO1lBQ3BCO1lBQ0EsSUFBSUUsU0FBUztnQkFDWHNGLE9BQU9JLFFBQVEsR0FBRzFGO1lBQ3BCO1lBRUEsZ0NBQWdDO1lBQ2hDLE1BQU0yRixXQUFXLE1BQU0vSSx1REFBV0EsQ0FBQ2dKLFdBQVcsQ0FBQ047WUFFL0MscUJBQXFCO1lBQ3JCLE1BQU1PLHNCQUFzQkYsU0FBU0csSUFBSSxDQUFDQyxHQUFHLENBQUMzRjtZQUU5QzdCLFlBQVlzSDtZQUNaNUcsZUFBZWtHO1lBQ2ZoRyxjQUFjd0csU0FBU0ssVUFBVSxDQUFDQyxXQUFXO1lBQzdDNUcsaUJBQWlCc0csU0FBU0ssVUFBVSxDQUFDRSxXQUFXO1lBRWhEdkgsV0FBVztRQUNiLEVBQUUsT0FBT3dILEtBQUs7Z0JBR1JBLGVBRU9BO1lBSlhDLFFBQVF4SCxLQUFLLENBQUMsNEJBQTRCdUg7WUFFMUMsSUFBSUEsRUFBQUEsZ0JBQUFBLElBQUlSLFFBQVEsY0FBWlEsb0NBQUFBLGNBQWNFLE1BQU0sTUFBSyxLQUFLO2dCQUNoQ3hILFNBQVM7WUFDWCxPQUFPLElBQUlzSCxFQUFBQSxpQkFBQUEsSUFBSVIsUUFBUSxjQUFaUSxxQ0FBQUEsZUFBY0UsTUFBTSxNQUFLLEtBQUs7Z0JBQ3ZDeEgsU0FBUztZQUNYLE9BQU8sSUFBSXNILElBQUlHLE9BQU8sQ0FBQ0MsUUFBUSxDQUFDLFVBQVU7Z0JBQ3hDMUgsU0FBUztZQUNYLE9BQU87Z0JBQ0xBLFNBQVMsVUFBc0IsT0FBWnNILElBQUlHLE9BQU87WUFDaEM7WUFFQS9ILFlBQVksRUFBRTtZQUNkSSxXQUFXO1FBQ2I7SUFDRjtJQUVBLCtFQUErRTtJQUMvRSxNQUFNd0Msb0JBQW9CLENBQUNGLFFBQVFaO1FBQ2pDLElBQUlBLFdBQVdBLFFBQVF3QixZQUFZLElBQUl4QixRQUFRMEIsWUFBWSxFQUFFO1lBQzNELE9BQU8sR0FBMkIxQixPQUF4QkEsUUFBUXdCLFlBQVksRUFBQyxLQUF3QixPQUFyQnhCLFFBQVEwQixZQUFZO1FBQ3hEO1FBQ0EsT0FBTztJQUNUO0lBRUEscUJBQXFCO0lBQ3JCdEYsZ0RBQVNBO3VDQUFDO1lBQ1J5STtRQUNGO3NDQUFHLEVBQUU7SUFFTCxtREFBbUQ7SUFDbkR6SSxnREFBU0E7dUNBQUM7WUFDUixpQ0FBaUM7WUFDakMsTUFBTTJJLFFBQVF2SSx5REFBWUE7WUFDMUIsSUFBSSxDQUFDdUksT0FBTztnQkFDViw4Q0FBOEM7Z0JBQzlDdkcsU0FBUztnQkFDVEYsV0FBVztnQkFDWDtZQUNGO1lBRUF1RztRQUNGO3NDQUFHLEVBQUU7SUFFTCxtRkFBbUY7SUFDbkYsTUFBTXNCLHVCQUF1QixDQUFDQztRQUM1QixJQUFJQSxhQUFhQSxVQUFVQyxNQUFNLElBQUksR0FBRztZQUN0QyxNQUFNQyxXQUFXRixVQUFVRyxTQUFTLENBQUMsR0FBRztZQUN4QyxJQUFJLENBQUNDLE1BQU1GLFdBQVc7Z0JBQ3BCLE9BQU8sR0FBdUQsT0FBcEQsSUFBSyxLQUFJRyxPQUFPQyxXQUFXLEtBQUtDLFNBQVNMLFNBQVEsR0FBRztZQUNoRTtRQUNGO1FBQ0EsT0FBTztJQUNUO0lBRUEsdUNBQXVDO0lBQ3ZDbEssZ0RBQVNBO3VDQUFDO1lBQ1IsTUFBTXdLO2tFQUFxQixDQUFDQztvQkFDMUIsSUFBSS9JLFlBQVlnSixPQUFPLElBQUksQ0FBQ2hKLFlBQVlnSixPQUFPLENBQUNDLFFBQVEsQ0FBQ0YsTUFBTUcsTUFBTSxHQUFHO3dCQUN0RWhKLHNCQUFzQjtvQkFDeEI7Z0JBQ0Y7O1lBRUFpSixTQUFTQyxnQkFBZ0IsQ0FBQyxhQUFhTjtZQUN2QzsrQ0FBTztvQkFDTEssU0FBU0UsbUJBQW1CLENBQUMsYUFBYVA7Z0JBQzVDOztRQUNGO3NDQUFHLEVBQUU7SUFFTCx5Q0FBeUM7SUFDekMsTUFBTVEsb0JBQW9CLENBQUNDO1FBQ3pCLE1BQU1DLFFBQVE7ZUFBSSxJQUFJQyxJQUFJRixhQUFhM0IsR0FBRyxDQUFDMUYsQ0FBQUEsVUFBV0EsUUFBUWEsSUFBSSxFQUFFMkcsTUFBTSxDQUFDM0csQ0FBQUEsT0FBUUEsUUFBUUEsU0FBUztTQUFRO1FBQzVHLE9BQU95RyxNQUFNRyxJQUFJO0lBQ25CO0lBRUEsNEJBQTRCO0lBQzVCLE1BQU1DLHFCQUFxQixDQUFDTDtRQUMxQixNQUFNTSxRQUFRLENBQUM7UUFDZk4sYUFBYU8sT0FBTyxDQUFDNUgsQ0FBQUE7WUFDbkIsSUFBSUEsUUFBUVcsVUFBVSxJQUFJWCxRQUFRVyxVQUFVLEtBQUssT0FBTztnQkFDdERnSCxLQUFLLENBQUMzSCxRQUFRVyxVQUFVLENBQUMsR0FBRyxDQUFDZ0gsS0FBSyxDQUFDM0gsUUFBUVcsVUFBVSxDQUFDLElBQUksS0FBSztZQUNqRTtRQUNGO1FBQ0EsT0FBT2tILE9BQU9DLE9BQU8sQ0FBQ0gsT0FBT2pDLEdBQUcsQ0FBQztnQkFBQyxDQUFDL0UsWUFBWW9ILE1BQU07bUJBQU07Z0JBQUVwSDtnQkFBWW9IO1lBQU07O0lBQ2pGO0lBRUEsc0RBQXNEO0lBQ3RELE1BQU1DLDJCQUEyQjtRQUMvQixJQUFJLENBQUM1SyxvQkFBb0IsT0FBTyxFQUFFO1FBQ2xDLE1BQU1rSyxRQUFRbkosWUFDWHFKLE1BQU0sQ0FBQ1MsQ0FBQUEsSUFBS0EsRUFBRXRILFVBQVUsS0FBS3ZELHNCQUFzQjZLLEVBQUVwSCxJQUFJLElBQUlvSCxFQUFFcEgsSUFBSSxLQUFLLE9BQ3hFNkUsR0FBRyxDQUFDdUMsQ0FBQUE7WUFDSCxvRUFBb0U7WUFDcEUsTUFBTUMsUUFBUUQsRUFBRXBILElBQUksQ0FBQ3NILEtBQUssQ0FBQztZQUMzQixPQUFPRCxNQUFNN0IsTUFBTSxLQUFLLElBQUk2QixLQUFLLENBQUMsRUFBRSxHQUFHO1FBQ3pDLEdBQ0NWLE1BQU0sQ0FBQ1ksQ0FBQUEsSUFBS0EsR0FDWjFDLEdBQUcsQ0FBQzJDLFFBQ0piLE1BQU0sQ0FBQ1ksQ0FBQUEsSUFBSyxDQUFDNUIsTUFBTTRCO1FBQ3RCLHdCQUF3QjtRQUN4QixPQUFPRSxNQUFNQyxJQUFJLENBQUMsSUFBSWhCLElBQUlELFFBQVFHLElBQUksQ0FBQyxDQUFDZSxHQUFHQyxJQUFNQSxJQUFJRDtJQUN2RDtJQUVBLDJEQUEyRDtJQUMzRCxNQUFNRSxzQkFBc0I7UUFDMUIsSUFBSUMsV0FBV3hLO1FBQ2YsSUFBSWYsb0JBQW9CO1lBQ3RCdUwsV0FBV0EsU0FBU25CLE1BQU0sQ0FBQ1MsQ0FBQUEsSUFBS0EsRUFBRXRILFVBQVUsS0FBS3ZEO1FBQ25EO1FBQ0EsSUFBSW1DLHFCQUFxQjtZQUN2Qm9KLFdBQVdBLFNBQVNuQixNQUFNLENBQUNTLENBQUFBO2dCQUN6QixJQUFJLENBQUNBLEVBQUVwSCxJQUFJLElBQUlvSCxFQUFFcEgsSUFBSSxLQUFLLE9BQU8sT0FBTztnQkFDeEMsTUFBTXFILFFBQVFELEVBQUVwSCxJQUFJLENBQUNzSCxLQUFLLENBQUM7Z0JBQzNCLE9BQU9ELE1BQU03QixNQUFNLEtBQUssS0FBSzZCLEtBQUssQ0FBQyxFQUFFLEtBQUtVLE9BQU9ySjtZQUNuRDtRQUNGO1FBQ0EsSUFBSXJDLHFCQUFxQjtZQUN2QixNQUFNMkwsY0FBYzNMLG9CQUFvQjRMLFdBQVc7WUFDbkRILFdBQVdBLFNBQVNuQixNQUFNLENBQUN4SCxDQUFBQSxVQUN6QkEsUUFBUUksSUFBSSxDQUFDMEksV0FBVyxHQUFHNUMsUUFBUSxDQUFDMkMsZ0JBQ3BDN0ksUUFBUUUsVUFBVSxDQUFDNEksV0FBVyxHQUFHNUMsUUFBUSxDQUFDMkM7UUFFOUM7UUFDQSxjQUFjO1FBQ2RGLFdBQVdBLFNBQVNuQixNQUFNLENBQUN4SCxDQUFBQTtZQUN6QixNQUFNZSxPQUFPZ0ksV0FBVy9JLFFBQVFlLElBQUk7WUFDcEMsSUFBSXRCLFdBQVkrRyxDQUFBQSxNQUFNekYsU0FBU0EsT0FBT2dJLFdBQVd0SixRQUFPLEdBQUksT0FBTztZQUNuRSxJQUFJRSxXQUFZNkcsQ0FBQUEsTUFBTXpGLFNBQVNBLE9BQU9nSSxXQUFXcEosUUFBTyxHQUFJLE9BQU87WUFDbkUsT0FBTztRQUNUO1FBQ0EsT0FBT2dKO0lBQ1Q7SUFFQSxzREFBc0Q7SUFDdER2TSxnREFBU0E7dUNBQUM7WUFDUnlJLGNBQWMsSUFBSSxzQ0FBc0M7UUFDMUQ7c0NBQUc7UUFBQ3pIO1FBQW9CRTtRQUFjSjtRQUFxQnFDO1FBQXFCRTtRQUFTRTtRQUFTVjtLQUFTO0lBRTNHLHNFQUFzRTtJQUN0RSxNQUFNK0osbUJBQW1CL0ssVUFBVSxpREFBaUQ7SUFFcEYsTUFBTWdMLHFCQUFxQixDQUFDako7UUFDMUJ2QyxtQkFBbUJ1QztRQUNuQm5DLGlCQUFpQjtZQUFFLEdBQUdtQyxPQUFPO1FBQUM7UUFDOUJyQyxhQUFhO0lBQ2Y7SUFFQSxNQUFNdUwsbUJBQW1CO1FBQ3ZCekwsbUJBQW1CO1FBQ25CRSxhQUFhO1FBQ2JFLGlCQUFpQjtJQUNuQjtJQUVBLE1BQU1zTCwwQkFBMEI7UUFDOUI5TCxzQkFBc0I7UUFDdEJFLGdCQUFnQjtRQUNoQk4sY0FBYztJQUNoQjtJQUVBLE1BQU1tTSxhQUFhO1FBQ2pCekwsYUFBYTtJQUNmO0lBRUEsTUFBTTBMLGFBQWE7UUFDakIsSUFBSTtZQUNGL0ssV0FBVztZQUVYLGdDQUFnQztZQUNoQyxNQUFNZ0wsYUFBYSxDQUFDeko7Z0JBQ2xCLElBQUlBLFVBQVUsTUFBTUEsVUFBVSxRQUFRQSxVQUFVMEosV0FBVztvQkFDekQsT0FBTztnQkFDVDtnQkFDQSx1QkFBdUI7Z0JBQ3ZCLElBQUksT0FBTzFKLFVBQVUsVUFBVTtvQkFDN0IsTUFBTTJKLFVBQVUzSixNQUFNVSxJQUFJO29CQUMxQixPQUFPaUosWUFBWSxLQUFLLE9BQU9BO2dCQUNqQztnQkFDQSxPQUFPM0o7WUFDVDtZQUVBLDBDQUEwQztZQUMxQyxNQUFNNEosb0JBQW9CLENBQUM1SjtnQkFDekIsSUFBSUEsVUFBVSxNQUFNQSxVQUFVLFFBQVFBLFVBQVUwSixXQUFXO29CQUN6RCxPQUFPO2dCQUNUO2dCQUNBLElBQUksT0FBTzFKLFVBQVUsVUFBVTtvQkFDN0IsTUFBTTJKLFVBQVUzSixNQUFNVSxJQUFJO29CQUMxQixJQUFJaUosWUFBWSxJQUFJLE9BQU87b0JBQzNCLE1BQU1FLFNBQVMvQyxTQUFTNkM7b0JBQ3hCLE9BQU9oRCxNQUFNa0QsVUFBVSxPQUFPQTtnQkFDaEM7Z0JBQ0EsSUFBSSxPQUFPN0osVUFBVSxVQUFVO29CQUM3QixPQUFPMkcsTUFBTTNHLFNBQVMsT0FBT0E7Z0JBQy9CO2dCQUNBLE9BQU87WUFDVDtZQUVBLHNEQUFzRDtZQUN0RCxNQUFNOEosbUJBQW1CLENBQUM5SjtnQkFDeEIsSUFBSUEsVUFBVSxNQUFNQSxVQUFVLFFBQVFBLFVBQVUwSixXQUFXO29CQUN6RCxPQUFPO2dCQUNUO2dCQUNBLE9BQU8sT0FBTzFKLFVBQVUsV0FBV0EsTUFBTVUsSUFBSSxLQUFLcUksT0FBTy9JLE9BQU9VLElBQUk7WUFDdEU7WUFFQSwwQkFBMEI7WUFDMUIsTUFBTXFKLFlBQVloTSxjQUFjd0MsSUFBSSxHQUFHeEMsY0FBY3dDLElBQUksQ0FBQ0csSUFBSSxHQUFHNEgsS0FBSyxDQUFDLE9BQU8sRUFBRTtZQUNoRixNQUFNMEIsWUFBWUQsU0FBUyxDQUFDLEVBQUUsSUFBSTtZQUNsQyxNQUFNRSxXQUFXRixVQUFVRyxLQUFLLENBQUMsR0FBR0MsSUFBSSxDQUFDLFFBQVE7WUFFakQsc0NBQXNDO1lBQ3RDLE1BQU1DLGFBQWE7Z0JBQ2pCLG1EQUFtRDtnQkFDbkQ1SixZQUFZc0osaUJBQWlCRTtnQkFDN0J2SixXQUFXcUosaUJBQWlCRztnQkFDNUIzSixZQUFZd0osaUJBQWlCL0wsY0FBY3NDLFVBQVU7Z0JBQ3JETyxlQUFlNkksV0FBVzFMLGNBQWM0QyxLQUFLO2dCQUM3Q0UsT0FBT2lKLGlCQUFpQi9MLGNBQWM4QyxLQUFLO2dCQUMzQ0UsUUFBUStJLGlCQUFpQi9MLGNBQWMrQyxVQUFVO2dCQUNqREssS0FBSzJJLGlCQUFpQi9MLGNBQWNvRCxHQUFHO2dCQUV2Qyw4Q0FBOEM7Z0JBQzlDUSxjQUFjaUksa0JBQWtCN0wsY0FBYzRELFlBQVk7Z0JBQzFERSxjQUFjK0gsa0JBQWtCN0wsY0FBYzhELFlBQVk7Z0JBRTFELG1CQUFtQjtnQkFDbkJQLGVBQWVtSSxXQUFXMUwsY0FBY3NELFdBQVc7Z0JBQ25ERCxTQUFTMEksaUJBQWlCL0wsY0FBY3FELE9BQU87Z0JBQy9DdUMsTUFBTW1HLGlCQUFpQi9MLGNBQWM0RixJQUFJO2dCQUN6Q0MsVUFBVWtHLGlCQUFpQi9MLGNBQWM2RixRQUFRO2dCQUNqREMsT0FBT2lHLGlCQUFpQi9MLGNBQWM4RixLQUFLO2dCQUMzQ0MsU0FBU2dHLGlCQUFpQi9MLGNBQWMrRixPQUFPO2dCQUMvQ0UsU0FBUzhGLGlCQUFpQi9MLGNBQWNpRyxPQUFPO2dCQUMvQ3hDLGdCQUFnQnNJLGlCQUFpQi9MLGNBQWN3RCxhQUFhO2dCQUM1REUsV0FBV3FJLGlCQUFpQi9MLGNBQWMwRCxTQUFTO2dCQUNuREMsUUFBUStHLE1BQU00QixPQUFPLENBQUN0TSxjQUFjMkQsTUFBTSxJQUN0QzNELGNBQWMyRCxNQUFNLENBQUNpRyxNQUFNLENBQUMyQyxDQUFBQSxRQUFTQSxTQUFTQSxNQUFNNUosSUFBSSxJQUFJeUosSUFBSSxDQUFDLFFBQ2pFTCxpQkFBaUIvTCxjQUFjMkQsTUFBTTtnQkFFekMsa0RBQWtEO2dCQUNsRG1CLFlBQVlpSCxpQkFBaUIvTCxjQUFjOEUsVUFBVTtnQkFDckRFLGtCQUFrQitHLGlCQUFpQi9MLGNBQWNnRixnQkFBZ0I7Z0JBQ2pFTSxhQUFheUcsaUJBQWlCL0wsY0FBY3NGLFdBQVc7Z0JBQ3ZERixjQUFjMkcsaUJBQWlCL0wsY0FBY29GLFlBQVk7Z0JBQ3pERix1QkFBdUI2RyxpQkFBaUIvTCxjQUFja0YscUJBQXFCO2dCQUMzRU0sZ0JBQWdCdUcsaUJBQWlCL0wsY0FBY3dGLGNBQWM7Z0JBQzdERSxzQkFBc0JxRyxpQkFBaUIvTCxjQUFjMEYsb0JBQW9CO2dCQUV6RTFCLGNBQWMrSCxpQkFBaUIvTCxjQUFjZ0UsWUFBWTtnQkFDekRFLG9CQUFvQjZILGlCQUFpQi9MLGNBQWNrRSxrQkFBa0I7Z0JBQ3JFTSxlQUFldUgsaUJBQWlCL0wsY0FBY3dFLGFBQWE7Z0JBQzNERixnQkFBZ0J5SCxpQkFBaUIvTCxjQUFjc0UsY0FBYztnQkFDN0RGLHlCQUF5QjJILGlCQUFpQi9MLGNBQWNvRSx1QkFBdUI7Z0JBQy9FTSxrQkFBa0JxSCxpQkFBaUIvTCxjQUFjMEUsZ0JBQWdCO2dCQUNqRUUsd0JBQXdCbUgsaUJBQWlCL0wsY0FBYzRFLHNCQUFzQjtZQUMvRTtZQUVBLG1DQUFtQztZQUNuQyxJQUFJNUUsY0FBYzRHLGNBQWMsSUFBSThELE1BQU00QixPQUFPLENBQUN0TSxjQUFjNEcsY0FBYyxHQUFHO2dCQUMvRTVHLGNBQWM0RyxjQUFjLENBQUNvRCxPQUFPLENBQUN3QyxDQUFBQTtvQkFDbkMsSUFBSUEsYUFBYUMsUUFBUSxJQUFJLEtBQUtELGFBQWFDLFFBQVEsSUFBSSxLQUFLRCxhQUFhckosSUFBSSxFQUFFO3dCQUNqRmtKLFVBQVUsQ0FBQyxXQUFpQyxPQUF0QkcsYUFBYUMsUUFBUSxFQUFDLFNBQU8sR0FBR1YsaUJBQWlCUyxhQUFhckosSUFBSTtvQkFDMUY7Z0JBQ0Y7WUFDRjtZQUVBLHNFQUFzRTtZQUN0RSxNQUFNdUosb0JBQW9CekMsT0FBTzBDLFdBQVcsQ0FDMUMxQyxPQUFPQyxPQUFPLENBQUNtQyxZQUFZekMsTUFBTSxDQUFDO29CQUFDLENBQUNnRCxLQUFLM0ssTUFBTTtnQkFDN0Msa0ZBQWtGO2dCQUNsRixNQUFNNEssaUJBQWlCO29CQUFDO29CQUFjO29CQUFhO29CQUFjO2lCQUFNO2dCQUN2RSxJQUFJQSxlQUFldkUsUUFBUSxDQUFDc0UsTUFBTTtvQkFDaEMsT0FBTzNLLFVBQVUsUUFBUUEsVUFBVTBKO2dCQUNyQztnQkFDQSxPQUFPMUosVUFBVSxRQUFRQSxVQUFVMEosYUFBYTFKLFVBQVU7WUFDNUQ7WUFHRix3REFBd0Q7WUFDeEQsSUFBSSxDQUFDeUssa0JBQWtCakssVUFBVSxFQUFFaUssa0JBQWtCakssVUFBVSxHQUFHO1lBQ2xFLElBQUksQ0FBQ2lLLGtCQUFrQmhLLFNBQVMsRUFBRWdLLGtCQUFrQmhLLFNBQVMsR0FBRztZQUNoRSxJQUFJLENBQUNnSyxrQkFBa0JuSyxVQUFVLEVBQUVtSyxrQkFBa0JuSyxVQUFVLEdBQUcsUUFBbUIsT0FBWHNHLEtBQUtpRSxHQUFHO1lBQ2xGLElBQUksQ0FBQ0osa0JBQWtCdEosR0FBRyxFQUFFc0osa0JBQWtCdEosR0FBRyxHQUFHO1lBRXBELGdCQUFnQjtZQUNoQitFLFFBQVE0RSxHQUFHLENBQUMsMkJBQTJCL007WUFDdkNtSSxRQUFRNEUsR0FBRyxDQUFDLDJCQUEyQkw7WUFDdkN2RSxRQUFRNEUsR0FBRyxDQUFDLGVBQWUvTSxjQUFjcUMsRUFBRTtZQUUzQyxrQ0FBa0M7WUFDbEMsTUFBTTJLLGlCQUFpQixNQUFNck8sdURBQVdBLENBQUNzTyxhQUFhLENBQUNqTixjQUFjcUMsRUFBRSxFQUFFcUs7WUFFekUsd0RBQXdEO1lBQ3hELE1BQU1RLHFCQUFxQjtnQkFDekIsR0FBR2xOLGFBQWE7Z0JBQ2hCLEdBQUdnTixjQUFjO2dCQUNqQnhLLE1BQU0sR0FBc0N3SyxPQUFuQ0EsZUFBZXZLLFVBQVUsSUFBSSxJQUFHLEtBQWtDLE9BQS9CdUssZUFBZXRLLFNBQVMsSUFBSSxJQUFLQyxJQUFJO2dCQUNqRkwsWUFBWTBLLGVBQWV6SyxVQUFVO2dCQUNyQ0ssT0FBT29LLGVBQWVuSyxhQUFhO2dCQUNuQ0UsWUFBWWlLLGVBQWVoSyxNQUFNO2dCQUNqQ0ksS0FBSzRKLGVBQWU1SixHQUFHO1lBQ3pCO1lBRUE5QyxZQUFZNk0sQ0FBQUEsT0FDVkEsS0FBS3JGLEdBQUcsQ0FBQzFGLENBQUFBLFVBQ1BBLFFBQVFDLEVBQUUsS0FBS3JDLGNBQWNxQyxFQUFFLEdBQUc2SyxxQkFBcUI5SztZQUkzRHZDLG1CQUFtQnFOO1lBQ25Cbk4sYUFBYTtZQUViLHVCQUF1QjtZQUN2QnFOLE1BQU07UUFFUixFQUFFLE9BQU96TSxPQUFPO2dCQVlWQTtZQVhKd0gsUUFBUXhILEtBQUssQ0FBQywyQkFBMkJBO1lBRXpDLDhCQUE4QjtZQUM5QixJQUFJQSxNQUFNK0csUUFBUSxFQUFFO2dCQUNsQlMsUUFBUXhILEtBQUssQ0FBQywwQkFBMEJBLE1BQU0rRyxRQUFRLENBQUNVLE1BQU07Z0JBQzdERCxRQUFReEgsS0FBSyxDQUFDLHdCQUF3QkEsTUFBTStHLFFBQVEsQ0FBQ0csSUFBSTtnQkFDekRNLFFBQVF4SCxLQUFLLENBQUMsMkJBQTJCQSxNQUFNK0csUUFBUSxDQUFDMkYsT0FBTztZQUNqRTtZQUVBLElBQUlDLGVBQWU7WUFFbkIsS0FBSTNNLGtCQUFBQSxNQUFNK0csUUFBUSxjQUFkL0csc0NBQUFBLGdCQUFnQmtILElBQUksRUFBRTtnQkFDeEIsMkJBQTJCO2dCQUMzQixJQUFJLE9BQU9sSCxNQUFNK0csUUFBUSxDQUFDRyxJQUFJLEtBQUssVUFBVTtvQkFDM0MsTUFBTTBGLGVBQWUsRUFBRTtvQkFDdkIsS0FBSyxNQUFNLENBQUNDLE9BQU9DLFNBQVMsSUFBSXhELE9BQU9DLE9BQU8sQ0FBQ3ZKLE1BQU0rRyxRQUFRLENBQUNHLElBQUksRUFBRzt3QkFDbkUsSUFBSTZDLE1BQU00QixPQUFPLENBQUNtQixXQUFXOzRCQUMzQkYsYUFBYUcsSUFBSSxDQUFDLEdBQWFELE9BQVZELE9BQU0sTUFBd0IsT0FBcEJDLFNBQVNyQixJQUFJLENBQUM7d0JBQy9DLE9BQU87NEJBQ0xtQixhQUFhRyxJQUFJLENBQUMsR0FBYUQsT0FBVkQsT0FBTSxNQUFhLE9BQVRDO3dCQUNqQztvQkFDRjtvQkFDQSxJQUFJRixhQUFhOUUsTUFBTSxHQUFHLEdBQUc7d0JBQzNCNkUsZUFBZSx1QkFBK0MsT0FBeEJDLGFBQWFuQixJQUFJLENBQUM7b0JBQzFEO2dCQUNGLE9BQU8sSUFBSXpMLE1BQU0rRyxRQUFRLENBQUNHLElBQUksQ0FBQzhGLE1BQU0sRUFBRTtvQkFDckNMLGVBQWUzTSxNQUFNK0csUUFBUSxDQUFDRyxJQUFJLENBQUM4RixNQUFNO2dCQUMzQyxPQUFPLElBQUloTixNQUFNK0csUUFBUSxDQUFDRyxJQUFJLENBQUNRLE9BQU8sRUFBRTtvQkFDdENpRixlQUFlM00sTUFBTStHLFFBQVEsQ0FBQ0csSUFBSSxDQUFDUSxPQUFPO2dCQUM1QztZQUNGO1lBRUErRSxNQUFNLGtCQUErQixPQUFiRTtRQUMxQixTQUFVO1lBQ1I1TSxXQUFXO1FBQ2I7SUFDRjtJQUVBLE1BQU1rTixlQUFlO1FBQ25CM04saUJBQWlCO1lBQUUsR0FBR0wsZUFBZTtRQUFDO1FBQ3RDRyxhQUFhO0lBQ2Y7SUFFQSxNQUFNOE4sb0JBQW9CLENBQUNMLE9BQU92TDtRQUNoQ2hDLGlCQUFpQmtOLENBQUFBLE9BQVM7Z0JBQ3hCLEdBQUdBLElBQUk7Z0JBQ1AsQ0FBQ0ssTUFBTSxFQUFFdkw7WUFDWDtJQUNGO0lBRUEsNEJBQTRCO0lBQzVCLE1BQU02TCxjQUFjO1FBQ2xCaE4sY0FBYztRQUNkbUc7SUFDRjtJQUVBLGdEQUFnRDtJQUNoRCxNQUFNOEcsZUFBZTtRQUNuQkMsT0FBT0MsSUFBSSxDQUFDO0lBQ2Q7SUFFQSxNQUFNQyxlQUFlO1FBQ25CLHNEQUFzRDtRQUN0RDNPLHVCQUF1Qkg7UUFDdkI0QixlQUFlO0lBQ2pCO0lBRUEsb0JBQW9CO0lBQ3BCLE1BQU1tTixtQkFBbUIsQ0FBQ0M7UUFDeEIsSUFBSUEsV0FBVyxLQUFLQSxXQUFXbk4sWUFBWTtZQUN6Q2dHLGNBQWNtSDtRQUNoQjtJQUNGO0lBRUEsNkJBQTZCO0lBQzdCLE1BQU1DLDBCQUEwQixDQUFDQztRQUMvQmpQLGNBQWNpUCxFQUFFbEYsTUFBTSxDQUFDbkgsS0FBSztJQUM1Qix5REFBeUQ7SUFDM0Q7SUFFQSxnQ0FBZ0M7SUFDaEMsTUFBTXNNLHNCQUFzQixDQUFDRDtRQUMzQixJQUFJQSxFQUFFMUIsR0FBRyxLQUFLLFNBQVM7WUFDckIwQixFQUFFRSxjQUFjO1lBQ2hCTjtRQUNGO0lBQ0Y7SUFFQSxJQUFJek4sU0FBUyxxQkFDWCw4REFBQ2dPO1FBQUlDLFdBQVU7a0JBQ2IsNEVBQUNEO1lBQUlDLFdBQVU7OzhCQUNiLDhEQUFDRDtvQkFBSUMsV0FBVTs7Ozs7OzhCQUNmLDhEQUFDRDtvQkFBSUMsV0FBVTs4QkFBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBS3JDLElBQUkvTixTQUFTTixTQUFTb0ksTUFBTSxLQUFLLEdBQUcscUJBQ2xDLDhEQUFDZ0c7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0Q7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0M7NEJBQUVELFdBQVU7c0NBQTZCOzs7Ozs7c0NBQzFDLDhEQUFDQztzQ0FBR2hPOzs7Ozs7c0NBQ0osOERBQUM4Tjs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNDOzhDQUFFOzs7Ozs7OENBQ0gsOERBQUNDO29DQUFHRixXQUFVOztzREFDWiw4REFBQ0c7c0RBQUc7Ozs7OztzREFDSiw4REFBQ0E7c0RBQUc7Ozs7OztzREFDSiw4REFBQ0E7c0RBQUc7Ozs7OztzREFDSiw4REFBQ0E7c0RBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFJViw4REFBQ0o7b0JBQUlDLFdBQVU7O3dCQUNaLENBQUMvTixNQUFNMkgsUUFBUSxDQUFDLDBCQUNmLDhEQUFDd0c7NEJBQ0NDLFNBQVNqQjs0QkFDVFksV0FBVTs0QkFDVk0sVUFBVW5POzs4Q0FFViw4REFBQzNDLHlJQUFTQTtvQ0FBQ3dRLFdBQVcsV0FBNEMsT0FBakM3TixhQUFhLGlCQUFpQjs7Ozs7O2dDQUM5REEsYUFBYSxnQkFBZ0I7Ozs7Ozs7c0NBR2xDLDhEQUFDaU87NEJBQ0NDLFNBQVMsSUFBTWYsT0FBT2lCLFFBQVEsQ0FBQ0MsSUFBSSxHQUFHOzRCQUN0Q1IsV0FBVTs7OENBRVYsOERBQUNyUSwwSUFBSUE7b0NBQUNxUSxXQUFVOzs7Ozs7Z0NBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQVF0QyxxQkFDRSw4REFBQ0Q7UUFBSUMsV0FBVTtrQkFDWixDQUFDOU8sZ0NBQ0E7c0JBQ0csQ0FBQ0osbUNBQ0EsOERBQUNULHdEQUFlQTtnQkFDZEksbUJBQW1CQTtnQkFDbkJzQyxpQkFBaUJBO2dCQUNqQmxCLGFBQWFBO2dCQUNiNE8sVUFBVTFQOzs7Ozt1QkFFVixDQUFDa0Msb0NBQ0gsOERBQUMzQyx5REFBZ0JBO2dCQUNmb1EsZUFBZSxHQUFFalEsMEJBQUFBLGtCQUFrQmtRLElBQUksQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRXJOLEtBQUssS0FBS3pDLGlDQUF4Q0wsOENBQUFBLHdCQUE2RCtDLEtBQUs7Z0JBQ25GcU4sUUFBUWhFO2dCQUNSbkIsMEJBQTBCQTtnQkFDMUI3SixhQUFhQTtnQkFDYmYsb0JBQW9CQTtnQkFDcEJnUSxjQUFjNU47Ozs7O3FDQUdoQiw4REFBQzNDLG9EQUFXQTtnQkFDVm1RLGVBQWUsR0FBRWpRLDJCQUFBQSxrQkFBa0JrUSxJQUFJLENBQUNDLENBQUFBLElBQUtBLEVBQUVyTixLQUFLLEtBQUt6QyxpQ0FBeENMLCtDQUFBQSx5QkFBNkQrQyxLQUFLO2dCQUNuRnVOLGFBQWE5TjtnQkFDYjROLFFBQVEsSUFBTTNOLHVCQUF1QjtnQkFDckN4QyxZQUFZQTtnQkFDWmlQLHlCQUF5QkE7Z0JBQ3pCRSxxQkFBcUJBO2dCQUNyQjFNLFNBQVNBO2dCQUNUQyxZQUFZQTtnQkFDWkMsU0FBU0E7Z0JBQ1RDLFlBQVlBO2dCQUNaa00sY0FBY0E7Z0JBQ2RwRCxxQkFBcUJBO2dCQUNyQi9KLGFBQWFBO2dCQUNib04sa0JBQWtCQTtnQkFDbEI5QyxvQkFBb0JBO2dCQUNwQjVLLFNBQVNBOzs7Ozs7MENBS2YsOERBQUMzQix1REFBY0E7WUFDYmMsaUJBQWlCQTtZQUNqQkksZUFBZUE7WUFDZkYsV0FBV0E7WUFDWHdMLGtCQUFrQkE7WUFDbEJFLFlBQVlBO1lBQ1pDLFlBQVlBO1lBQ1ptQyxjQUFjQTtZQUNkQyxtQkFBbUJBO1lBQ25CMU8sbUJBQW1CQTs7Ozs7Ozs7Ozs7QUFLN0I7R0E3cEJ3QkQ7S0FBQUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccGdhdXRcXERvY3VtZW50c1xcVlMgQ09ERVxcY29tYmluZVxcZnJvbnRlbmRcXHNyY1xcYXBwXFxhZG1pblxcc3R1ZGVudC1tYW5hZ2VtZW50XFxwYWdlLmpzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7XG4gIEFycm93TGVmdCxcbiAgQ2FsZW5kYXIsXG4gIFJlZnJlc2hDdyxcbiAgU2F2ZSxcbiAgU2VhcmNoLFxuICBVc2VyLFxuICBYLFxuICBHcmFkdWF0aW9uQ2FwXG59IGZyb20gXCJsdWNpZGUtcmVhY3RcIjtcbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlUmVmLCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHN0dWRlbnRzQVBJIH0gZnJvbSAnLi4vLi4vLi4vYXBpL29wdGltaXplZCc7XG5pbXBvcnQgeyBnZXRBdXRoVG9rZW4gfSBmcm9tICcuLi8uLi8uLi91dGlscy9hdXRoJztcbmltcG9ydCBDdXN0b21Ecm9wZG93biBmcm9tICcuL1N0dWRlbnREcm9wZG93bic7XG5pbXBvcnQgU3R1ZGVudFByb2ZpbGUgZnJvbSAnLi9TdHVkZW50UHJvZmlsZSc7XG5pbXBvcnQgRGVwYXJ0bWVudENhcmRzIGZyb20gJy4vRGVwYXJ0bWVudENhcmRzJztcbmltcG9ydCBQYXNzb3V0WWVhckNhcmRzIGZyb20gJy4vUGFzc291dFllYXJDYXJkcyc7XG5pbXBvcnQgU3R1ZGVudExpc3QgZnJvbSAnLi9TdHVkZW50TGlzdCc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFN0dWRlbnRNYW5hZ2VtZW50KCkge1xuICBjb25zdCBbc2VhcmNoVGVybSwgc2V0U2VhcmNoVGVybV0gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IFtkZWJvdW5jZWRTZWFyY2hUZXJtLCBzZXREZWJvdW5jZWRTZWFyY2hUZXJtXSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW3NlbGVjdGVkRGVwYXJ0bWVudCwgc2V0U2VsZWN0ZWREZXBhcnRtZW50XSA9IHVzZVN0YXRlKG51bGwpO1xuICBjb25zdCBbc2VsZWN0ZWRZZWFyLCBzZXRTZWxlY3RlZFllYXJdID0gdXNlU3RhdGUoJ2FsbCcpO1xuICBjb25zdCBbc2VsZWN0ZWRTdHVkZW50LCBzZXRTZWxlY3RlZFN0dWRlbnRdID0gdXNlU3RhdGUobnVsbCk7XG4gIGNvbnN0IFtpc0VkaXRpbmcsIHNldElzRWRpdGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtlZGl0ZWRTdHVkZW50LCBzZXRFZGl0ZWRTdHVkZW50XSA9IHVzZVN0YXRlKG51bGwpO1xuICBjb25zdCBkcm9wZG93blJlZiA9IHVzZVJlZihudWxsKTtcbiAgY29uc3QgW2lzWWVhckRyb3Bkb3duT3Blbiwgc2V0SXNZZWFyRHJvcGRvd25PcGVuXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3N0dWRlbnRzLCBzZXRTdHVkZW50c10gPSB1c2VTdGF0ZShbXSk7XG4gIGNvbnN0IFthbGxTdHVkZW50cywgc2V0QWxsU3R1ZGVudHNdID0gdXNlU3RhdGUoW10pO1xuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcbiAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZShudWxsKTtcbiAgY29uc3QgW2lzUmV0cnlpbmcsIHNldElzUmV0cnlpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbY3VycmVudFBhZ2UsIHNldEN1cnJlbnRQYWdlXSA9IHVzZVN0YXRlKDEpO1xuICBjb25zdCBbdG90YWxQYWdlcywgc2V0VG90YWxQYWdlc10gPSB1c2VTdGF0ZSgxKTtcbiAgY29uc3QgW3RvdGFsU3R1ZGVudHMsIHNldFRvdGFsU3R1ZGVudHNdID0gdXNlU3RhdGUoMCk7XG4gIGNvbnN0IFtwYWdlU2l6ZSwgc2V0UGFnZVNpemVdID0gdXNlU3RhdGUoMTApO1xuICBjb25zdCBbYXZhaWxhYmxlWWVhcnMsIHNldEF2YWlsYWJsZVllYXJzXSA9IHVzZVN0YXRlKFtdKTtcbiAgY29uc3QgW2RlcGFydG1lbnRTdGF0cywgc2V0RGVwYXJ0bWVudFN0YXRzXSA9IHVzZVN0YXRlKFtdKTtcbiAgY29uc3QgW3NlbGVjdGVkUGFzc291dFllYXIsIHNldFNlbGVjdGVkUGFzc291dFllYXJdID0gdXNlU3RhdGUobnVsbCk7XG4gIGNvbnN0IFtjZ3BhTWluLCBzZXRDZ3BhTWluXSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW2NncGFNYXgsIHNldENncGFNYXhdID0gdXNlU3RhdGUoJycpO1xuXG4gIC8vIERyb3Bkb3duIG9wdGlvbnNcbiAgY29uc3QgZGVwYXJ0bWVudE9wdGlvbnMgPSBbXG4gICAgeyB2YWx1ZTogJ0NvbXB1dGVyIFNjaWVuY2UnLCBsYWJlbDogJ0NvbXB1dGVyIFNjaWVuY2UnIH0sXG4gICAgeyB2YWx1ZTogJ0VsZWN0cm9uaWNzJywgbGFiZWw6ICdFbGVjdHJvbmljcycgfSxcbiAgICB7IHZhbHVlOiAnTWVjaGFuaWNhbCcsIGxhYmVsOiAnTWVjaGFuaWNhbCcgfSxcbiAgICB7IHZhbHVlOiAnQ2l2aWwnLCBsYWJlbDogJ0NpdmlsJyB9LFxuICAgIHsgdmFsdWU6ICdFbGVjdHJpY2FsJywgbGFiZWw6ICdFbGVjdHJpY2FsJyB9LFxuICAgIHsgdmFsdWU6ICdJbmZvcm1hdGlvbiBUZWNobm9sb2d5JywgbGFiZWw6ICdJbmZvcm1hdGlvbiBUZWNobm9sb2d5JyB9LFxuICAgIHsgdmFsdWU6ICdDaGVtaWNhbCcsIGxhYmVsOiAnQ2hlbWljYWwnIH0sXG4gICAgeyB2YWx1ZTogJ0Jpb3RlY2hub2xvZ3knLCBsYWJlbDogJ0Jpb3RlY2hub2xvZ3knIH1cbiAgXTtcblxuICAvLyBUcmFuc2Zvcm0gc3R1ZGVudCBkYXRhIGZyb20gQVBJIHJlc3BvbnNlXG4gIGNvbnN0IHRyYW5zZm9ybVN0dWRlbnREYXRhID0gKHN0dWRlbnQpID0+ICh7XG4gICAgaWQ6IHN0dWRlbnQuaWQsXG4gICAgcm9sbE51bWJlcjogc3R1ZGVudC5zdHVkZW50X2lkIHx8ICdOL0EnLFxuICAgIG5hbWU6IGAke3N0dWRlbnQuZmlyc3RfbmFtZSB8fCAnJ30gJHtzdHVkZW50Lmxhc3RfbmFtZSB8fCAnJ31gLnRyaW0oKSB8fCAnVW5rbm93bicsXG4gICAgZW1haWw6IHN0dWRlbnQuY29udGFjdF9lbWFpbCB8fCBzdHVkZW50LmVtYWlsIHx8ICdOL0EnLFxuICAgIHBob25lOiBzdHVkZW50LnBob25lIHx8ICdOL0EnLFxuICAgIGRlcGFydG1lbnQ6IHN0dWRlbnQuYnJhbmNoIHx8ICdOL0EnLFxuICAgIHllYXI6IGdldFllYXJGcm9tQnJhbmNoKHN0dWRlbnQuYnJhbmNoLCBzdHVkZW50KSxcbiAgICBjZ3BhOiBzdHVkZW50LmdwYSB8fCAnTi9BJyxcbiAgICBncGE6IHN0dWRlbnQuZ3BhIHx8ICdOL0EnLCAvLyBPdmVyYWxsIENHUEEgZnJvbSBkYXRhYmFzZVxuICAgIGFkZHJlc3M6IHN0dWRlbnQuYWRkcmVzcyB8fCAnTi9BJyxcbiAgICBkYXRlT2ZCaXJ0aDogc3R1ZGVudC5kYXRlX29mX2JpcnRoIHx8ICcnLFxuICAgIHBhcmVudENvbnRhY3Q6IHN0dWRlbnQucGFyZW50X2NvbnRhY3QgfHwgJ04vQScsXG4gICAgZWR1Y2F0aW9uOiBzdHVkZW50LmVkdWNhdGlvbiB8fCAnTi9BJyxcbiAgICBza2lsbHM6IHN0dWRlbnQuc2tpbGxzIHx8IFtdLFxuXG4gICAgLy8gQWNhZGVtaWMgZGV0YWlsc1xuICAgICAgICBqb2luaW5nX3llYXI6IHN0dWRlbnQuam9pbmluZ195ZWFyIHx8IHN0dWRlbnQuYWRtaXNzaW9uX3llYXIgfHwgJycsXG4gICAgICAgIHBhc3NvdXRfeWVhcjogc3R1ZGVudC5wYXNzb3V0X3llYXIgfHwgc3R1ZGVudC5ncmFkdWF0aW9uX3llYXIgfHwgJycsXG5cbiAgICAgICAgLy8gQ2xhc3MgWElJIGRldGFpbHNcbiAgICAgICAgdHdlbGZ0aF9jZ3BhOiBzdHVkZW50LnR3ZWxmdGhfY2dwYSB8fCBzdHVkZW50LmNsYXNzXzEyX2NncGEgfHwgJycsXG4gICAgICAgIHR3ZWxmdGhfcGVyY2VudGFnZTogc3R1ZGVudC50d2VsZnRoX3BlcmNlbnRhZ2UgfHwgc3R1ZGVudC5jbGFzc18xMl9wZXJjZW50YWdlIHx8ICcnLFxuICAgICAgICB0d2VsZnRoX3llYXJfb2ZfcGFzc2luZzogc3R1ZGVudC50d2VsZnRoX3llYXJfb2ZfcGFzc2luZyB8fCBzdHVkZW50LmNsYXNzXzEyX3llYXIgfHwgJycsXG4gICAgICAgIHR3ZWxmdGhfc2Nob29sOiBzdHVkZW50LnR3ZWxmdGhfc2Nob29sIHx8IHN0dWRlbnQuY2xhc3NfMTJfc2Nob29sIHx8ICcnLFxuICAgICAgICB0d2VsZnRoX2JvYXJkOiBzdHVkZW50LnR3ZWxmdGhfYm9hcmQgfHwgc3R1ZGVudC5jbGFzc18xMl9ib2FyZCB8fCAnJyxcbiAgICAgICAgdHdlbGZ0aF9sb2NhdGlvbjogc3R1ZGVudC50d2VsZnRoX2xvY2F0aW9uIHx8IHN0dWRlbnQuY2xhc3NfMTJfbG9jYXRpb24gfHwgJycsXG4gICAgICAgIHR3ZWxmdGhfc3BlY2lhbGl6YXRpb246IHN0dWRlbnQudHdlbGZ0aF9zcGVjaWFsaXphdGlvbiB8fCBzdHVkZW50LmNsYXNzXzEyX3N0cmVhbSB8fCAnJyxcblxuICAgICAgICAvLyBDbGFzcyBYIGRldGFpbHNcbiAgICAgICAgdGVudGhfY2dwYTogc3R1ZGVudC50ZW50aF9jZ3BhIHx8IHN0dWRlbnQuY2xhc3NfMTBfY2dwYSB8fCAnJyxcbiAgICAgICAgdGVudGhfcGVyY2VudGFnZTogc3R1ZGVudC50ZW50aF9wZXJjZW50YWdlIHx8IHN0dWRlbnQuY2xhc3NfMTBfcGVyY2VudGFnZSB8fCAnJyxcbiAgICAgICAgdGVudGhfeWVhcl9vZl9wYXNzaW5nOiBzdHVkZW50LnRlbnRoX3llYXJfb2ZfcGFzc2luZyB8fCBzdHVkZW50LmNsYXNzXzEwX3llYXIgfHwgJycsXG4gICAgICAgIHRlbnRoX3NjaG9vbDogc3R1ZGVudC50ZW50aF9zY2hvb2wgfHwgc3R1ZGVudC5jbGFzc18xMF9zY2hvb2wgfHwgJycsXG4gICAgICAgIHRlbnRoX2JvYXJkOiBzdHVkZW50LnRlbnRoX2JvYXJkIHx8IHN0dWRlbnQuY2xhc3NfMTBfYm9hcmQgfHwgJycsXG4gICAgICAgIHRlbnRoX2xvY2F0aW9uOiBzdHVkZW50LnRlbnRoX2xvY2F0aW9uIHx8IHN0dWRlbnQuY2xhc3NfMTBfbG9jYXRpb24gfHwgJycsXG4gICAgICAgIHRlbnRoX3NwZWNpYWxpemF0aW9uOiBzdHVkZW50LnRlbnRoX3NwZWNpYWxpemF0aW9uIHx8IHN0dWRlbnQuY2xhc3NfMTBfc3RyZWFtIHx8ICcnLFxuXG4gICAgICAgIC8vIEFkZHJlc3MgZGV0YWlsc1xuICAgICAgICBjaXR5OiBzdHVkZW50LmNpdHkgfHwgJycsXG4gICAgICAgIGRpc3RyaWN0OiBzdHVkZW50LmRpc3RyaWN0IHx8ICcnLFxuICAgICAgICBzdGF0ZTogc3R1ZGVudC5zdGF0ZSB8fCAnJyxcbiAgICAgICAgcGluY29kZTogc3R1ZGVudC5waW5jb2RlIHx8IHN0dWRlbnQucGluX2NvZGUgfHwgJycsXG4gICAgICAgIGNvdW50cnk6IHN0dWRlbnQuY291bnRyeSB8fCAnSW5kaWEnLFxuXG4gICAgICAgIC8vIENlcnRpZmljYXRlIFVSTHNcbiAgICAgICAgdGVudGhfY2VydGlmaWNhdGU6IHN0dWRlbnQudGVudGhfY2VydGlmaWNhdGUgfHwgc3R1ZGVudC5jbGFzc18xMF9jZXJ0aWZpY2F0ZSB8fCAnJyxcbiAgICAgICAgdHdlbGZ0aF9jZXJ0aWZpY2F0ZTogc3R1ZGVudC50d2VsZnRoX2NlcnRpZmljYXRlIHx8IHN0dWRlbnQuY2xhc3NfMTJfY2VydGlmaWNhdGUgfHwgJycsXG4gICAgICAgIHRlbnRoX2NlcnRpZmljYXRlX3VybDogc3R1ZGVudC50ZW50aF9jZXJ0aWZpY2F0ZV91cmwgfHwgc3R1ZGVudC5jbGFzc18xMF9jZXJ0aWZpY2F0ZV91cmwgfHwgJycsXG4gICAgICAgIHR3ZWxmdGhfY2VydGlmaWNhdGVfdXJsOiBzdHVkZW50LnR3ZWxmdGhfY2VydGlmaWNhdGVfdXJsIHx8IHN0dWRlbnQuY2xhc3NfMTJfY2VydGlmaWNhdGVfdXJsIHx8ICcnLFxuXG4gICAgICAgIC8vIFJlc3VtZSBkZXRhaWxzXG4gICAgICAgIHJlc3VtZTogc3R1ZGVudC5yZXN1bWUgfHwgJycsXG4gICAgICAgIHJlc3VtZV91cmw6IHN0dWRlbnQucmVzdW1lX3VybCB8fCAnJyxcblxuICAgICAgICAvLyBTZW1lc3Rlci13aXNlIENHUEEgZGF0YSAtIHVzZSBhY3R1YWwgYmFja2VuZCBkYXRhXG4gICAgICAgIHNlbWVzdGVyX2NncGFzOiBzdHVkZW50LnNlbWVzdGVyX21hcmtzaGVldHMgfHwgW10sXG4gICAgICAgIHNlbWVzdGVyX21hcmtzaGVldHM6IHN0dWRlbnQuc2VtZXN0ZXJfbWFya3NoZWV0cyB8fCBbXSxcbiAgICAgIH0pO1xuXG4gIC8vIERlYm91bmNlIHNlYXJjaCB0ZXJtXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgdGltZXIgPSBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgIHNldERlYm91bmNlZFNlYXJjaFRlcm0oc2VhcmNoVGVybSk7XG4gICAgfSwgMzAwKTsgLy8gMzAwbXMgZGVsYXlcblxuICAgIHJldHVybiAoKSA9PiBjbGVhclRpbWVvdXQodGltZXIpO1xuICB9LCBbc2VhcmNoVGVybV0pO1xuXG4gIC8vIEZldGNoIHN0dWRlbnRzIHdpdGggc2VydmVyLXNpZGUgcGFnaW5hdGlvbiBhbmQgZmlsdGVyaW5nXG4gIGNvbnN0IGZldGNoU3R1ZGVudHMgPSBhc3luYyAocGFnZSA9IDEpID0+IHtcbiAgICB0cnkge1xuICAgICAgc2V0TG9hZGluZyh0cnVlKTtcbiAgICAgIHNldEVycm9yKG51bGwpO1xuICAgICAgc2V0SXNSZXRyeWluZyhmYWxzZSk7XG5cbiAgICAgIGNvbnN0IHRva2VuID0gZ2V0QXV0aFRva2VuKCk7XG4gICAgICBpZiAoIXRva2VuKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignTm8gYXV0aGVudGljYXRpb24gdG9rZW4gZm91bmQuIFBsZWFzZSBsb2dpbiBmaXJzdC4nKTtcbiAgICAgIH1cblxuICAgICAgLy8gQnVpbGQgZmlsdGVyIHBhcmFtZXRlcnMgZm9yIHNlcnZlci1zaWRlIGZpbHRlcmluZ1xuICAgICAgY29uc3QgcGFyYW1zID0ge1xuICAgICAgICBwYWdlLFxuICAgICAgICBwYWdlX3NpemU6IHBhZ2VTaXplLFxuICAgICAgfTtcblxuICAgICAgLy8gQWRkIHNlYXJjaCBmaWx0ZXJcbiAgICAgIGlmIChkZWJvdW5jZWRTZWFyY2hUZXJtKSB7XG4gICAgICAgIHBhcmFtcy5zZWFyY2ggPSBkZWJvdW5jZWRTZWFyY2hUZXJtO1xuICAgICAgfVxuXG4gICAgICAvLyBBZGQgZGVwYXJ0bWVudCBmaWx0ZXJcbiAgICAgIGlmIChzZWxlY3RlZERlcGFydG1lbnQpIHtcbiAgICAgICAgcGFyYW1zLmRlcGFydG1lbnQgPSBzZWxlY3RlZERlcGFydG1lbnQ7XG4gICAgICB9XG5cbiAgICAgIC8vIEFkZCB5ZWFyIGZpbHRlciAoY29udmVydCB0byBwYXNzb3V0IHllYXIgaWYgbmVlZGVkKVxuICAgICAgaWYgKHNlbGVjdGVkWWVhciAhPT0gJ2FsbCcpIHtcbiAgICAgICAgcGFyYW1zLnllYXIgPSBzZWxlY3RlZFllYXI7XG4gICAgICB9XG5cbiAgICAgIC8vIEFkZCBDR1BBIGZpbHRlcnNcbiAgICAgIGlmIChjZ3BhTWluKSB7XG4gICAgICAgIHBhcmFtcy5jZ3BhX21pbiA9IGNncGFNaW47XG4gICAgICB9XG4gICAgICBpZiAoY2dwYU1heCkge1xuICAgICAgICBwYXJhbXMuY2dwYV9tYXggPSBjZ3BhTWF4O1xuICAgICAgfVxuXG4gICAgICAvLyBGZXRjaCBkYXRhIGZyb20gb3B0aW1pemVkIEFQSVxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBzdHVkZW50c0FQSS5nZXRTdHVkZW50cyhwYXJhbXMpO1xuXG4gICAgICAvLyBUcmFuc2Zvcm0gdGhlIGRhdGFcbiAgICAgIGNvbnN0IHRyYW5zZm9ybWVkU3R1ZGVudHMgPSByZXNwb25zZS5kYXRhLm1hcCh0cmFuc2Zvcm1TdHVkZW50RGF0YSk7XG5cbiAgICAgIHNldFN0dWRlbnRzKHRyYW5zZm9ybWVkU3R1ZGVudHMpO1xuICAgICAgc2V0Q3VycmVudFBhZ2UocGFnZSk7XG4gICAgICBzZXRUb3RhbFBhZ2VzKHJlc3BvbnNlLnBhZ2luYXRpb24udG90YWxfcGFnZXMpO1xuICAgICAgc2V0VG90YWxTdHVkZW50cyhyZXNwb25zZS5wYWdpbmF0aW9uLnRvdGFsX2NvdW50KTtcblxuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBzdHVkZW50czonLCBlcnIpO1xuXG4gICAgICBpZiAoZXJyLnJlc3BvbnNlPy5zdGF0dXMgPT09IDQwMSkge1xuICAgICAgICBzZXRFcnJvcignQXV0aGVudGljYXRpb24gZmFpbGVkLiBQbGVhc2UgbG9naW4gYWdhaW4uJyk7XG4gICAgICB9IGVsc2UgaWYgKGVyci5yZXNwb25zZT8uc3RhdHVzID09PSA0MDMpIHtcbiAgICAgICAgc2V0RXJyb3IoJ1lvdSBkbyBub3QgaGF2ZSBwZXJtaXNzaW9uIHRvIHZpZXcgc3R1ZGVudHMuIEFkbWluIGFjY2VzcyByZXF1aXJlZC4nKTtcbiAgICAgIH0gZWxzZSBpZiAoZXJyLm1lc3NhZ2UuaW5jbHVkZXMoJ3Rva2VuJykpIHtcbiAgICAgICAgc2V0RXJyb3IoJ1BsZWFzZSBsb2dpbiB0byBhY2Nlc3Mgc3R1ZGVudCBtYW5hZ2VtZW50LicpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc2V0RXJyb3IoYEVycm9yOiAke2Vyci5tZXNzYWdlfWApO1xuICAgICAgfVxuXG4gICAgICBzZXRTdHVkZW50cyhbXSk7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gSGVscGVyIGZ1bmN0aW9uIHRvIGRldGVybWluZSB5ZWFyIGZyb20gYnJhbmNoICh5b3UgY2FuIGN1c3RvbWl6ZSB0aGlzIGxvZ2ljKVxuICBjb25zdCBnZXRZZWFyRnJvbUJyYW5jaCA9IChicmFuY2gsIHN0dWRlbnQpID0+IHtcbiAgICBpZiAoc3R1ZGVudCAmJiBzdHVkZW50LmpvaW5pbmdfeWVhciAmJiBzdHVkZW50LnBhc3NvdXRfeWVhcikge1xuICAgICAgcmV0dXJuIGAke3N0dWRlbnQuam9pbmluZ195ZWFyfS0ke3N0dWRlbnQucGFzc291dF95ZWFyfWA7XG4gICAgfVxuICAgIHJldHVybiAnTi9BJztcbiAgfTtcblxuICAvLyBJbml0aWFsIGRhdGEgZmV0Y2hcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBmZXRjaFN0dWRlbnRzKCk7XG4gIH0sIFtdKTtcblxuICAvLyBBZGQgdGhpcyB1c2VFZmZlY3QgYWZ0ZXIgeW91ciBleGlzdGluZyB1c2VFZmZlY3RcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyBDaGVjayBpZiB1c2VyIGlzIGF1dGhlbnRpY2F0ZWRcbiAgICBjb25zdCB0b2tlbiA9IGdldEF1dGhUb2tlbigpO1xuICAgIGlmICghdG9rZW4pIHtcbiAgICAgIC8vIFJlZGlyZWN0IHRvIGxvZ2luIHBhZ2Ugb3Igc2hvdyBsb2dpbiBwcm9tcHRcbiAgICAgIHNldEVycm9yKCdQbGVhc2UgbG9naW4gdG8gYWNjZXNzIHN0dWRlbnQgbWFuYWdlbWVudC4nKTtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICBcbiAgICBmZXRjaFN0dWRlbnRzKCk7XG4gIH0sIFtdKTtcblxuICAvLyBIZWxwZXIgZnVuY3Rpb24gdG8gZXh0cmFjdCB5ZWFyIGZyb20gc3R1ZGVudCBJRCAoYXNzdW1pbmcgZm9ybWF0IGxpa2UgQ1MyMDIxMDAxKVxuICBjb25zdCBnZXRZZWFyRnJvbVN0dWRlbnRJZCA9IChzdHVkZW50SWQpID0+IHtcbiAgICBpZiAoc3R1ZGVudElkICYmIHN0dWRlbnRJZC5sZW5ndGggPj0gNikge1xuICAgICAgY29uc3QgeWVhclBhcnQgPSBzdHVkZW50SWQuc3Vic3RyaW5nKDIsIDYpO1xuICAgICAgaWYgKCFpc05hTih5ZWFyUGFydCkpIHtcbiAgICAgICAgcmV0dXJuIGAkezQgLSAobmV3IERhdGUoKS5nZXRGdWxsWWVhcigpIC0gcGFyc2VJbnQoeWVhclBhcnQpKX10aCBZZWFyYDtcbiAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuICdVbmtub3duJztcbiAgfTtcblxuICAvLyBDbG9zZSBkcm9wZG93biB3aGVuIGNsaWNraW5nIG91dHNpZGVcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBoYW5kbGVDbGlja091dHNpZGUgPSAoZXZlbnQpID0+IHtcbiAgICAgIGlmIChkcm9wZG93blJlZi5jdXJyZW50ICYmICFkcm9wZG93blJlZi5jdXJyZW50LmNvbnRhaW5zKGV2ZW50LnRhcmdldCkpIHtcbiAgICAgICAgc2V0SXNZZWFyRHJvcGRvd25PcGVuKGZhbHNlKTtcbiAgICAgIH1cbiAgICB9O1xuXG4gICAgZG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcignbW91c2Vkb3duJywgaGFuZGxlQ2xpY2tPdXRzaWRlKTtcbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgZG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcignbW91c2Vkb3duJywgaGFuZGxlQ2xpY2tPdXRzaWRlKTtcbiAgICB9O1xuICB9LCBbXSk7XG5cbiAgLy8gR2V0IGF2YWlsYWJsZSB5ZWFycyBmcm9tIHN0dWRlbnRzIGRhdGFcbiAgY29uc3QgZ2V0QXZhaWxhYmxlWWVhcnMgPSAoc3R1ZGVudHNEYXRhKSA9PiB7XG4gICAgY29uc3QgeWVhcnMgPSBbLi4ubmV3IFNldChzdHVkZW50c0RhdGEubWFwKHN0dWRlbnQgPT4gc3R1ZGVudC55ZWFyKS5maWx0ZXIoeWVhciA9PiB5ZWFyICYmIHllYXIgIT09ICdOL0EnKSldO1xuICAgIHJldHVybiB5ZWFycy5zb3J0KCk7XG4gIH07XG5cbiAgLy8gR2V0IGRlcGFydG1lbnQgc3RhdGlzdGljc1xuICBjb25zdCBnZXREZXBhcnRtZW50U3RhdHMgPSAoc3R1ZGVudHNEYXRhKSA9PiB7XG4gICAgY29uc3Qgc3RhdHMgPSB7fTtcbiAgICBzdHVkZW50c0RhdGEuZm9yRWFjaChzdHVkZW50ID0+IHtcbiAgICAgIGlmIChzdHVkZW50LmRlcGFydG1lbnQgJiYgc3R1ZGVudC5kZXBhcnRtZW50ICE9PSAnTi9BJykge1xuICAgICAgICBzdGF0c1tzdHVkZW50LmRlcGFydG1lbnRdID0gKHN0YXRzW3N0dWRlbnQuZGVwYXJ0bWVudF0gfHwgMCkgKyAxO1xuICAgICAgfVxuICAgIH0pO1xuICAgIHJldHVybiBPYmplY3QuZW50cmllcyhzdGF0cykubWFwKChbZGVwYXJ0bWVudCwgY291bnRdKSA9PiAoeyBkZXBhcnRtZW50LCBjb3VudCB9KSk7XG4gIH07XG5cbiAgLy8gR2V0IGF2YWlsYWJsZSBwYXNzb3V0IHllYXJzIGZvciBzZWxlY3RlZCBkZXBhcnRtZW50XG4gIGNvbnN0IGdldEF2YWlsYWJsZVBhc3NvdXRZZWFycyA9ICgpID0+IHtcbiAgICBpZiAoIXNlbGVjdGVkRGVwYXJ0bWVudCkgcmV0dXJuIFtdO1xuICAgIGNvbnN0IHllYXJzID0gYWxsU3R1ZGVudHNcbiAgICAgIC5maWx0ZXIocyA9PiBzLmRlcGFydG1lbnQgPT09IHNlbGVjdGVkRGVwYXJ0bWVudCAmJiBzLnllYXIgJiYgcy55ZWFyICE9PSAnTi9BJylcbiAgICAgIC5tYXAocyA9PiB7XG4gICAgICAgIC8vIEV4dHJhY3QgcGFzc291dCB5ZWFyIGZyb20geWVhciBzdHJpbmcgKGZvcm1hdDogXCJqb2luaW5nLXBhc3NvdXRcIilcbiAgICAgICAgY29uc3QgcGFydHMgPSBzLnllYXIuc3BsaXQoJy0nKTtcbiAgICAgICAgcmV0dXJuIHBhcnRzLmxlbmd0aCA9PT0gMiA/IHBhcnRzWzFdIDogbnVsbDtcbiAgICAgIH0pXG4gICAgICAuZmlsdGVyKHkgPT4geSlcbiAgICAgIC5tYXAoTnVtYmVyKVxuICAgICAgLmZpbHRlcih5ID0+ICFpc05hTih5KSk7XG4gICAgLy8gVW5pcXVlIGFuZCBkZXNjZW5kaW5nXG4gICAgcmV0dXJuIEFycmF5LmZyb20obmV3IFNldCh5ZWFycykpLnNvcnQoKGEsIGIpID0+IGIgLSBhKTtcbiAgfTtcblxuICAvLyBGaWx0ZXIgc3R1ZGVudHMgZm9yIHNlbGVjdGVkIGRlcGFydG1lbnQgYW5kIHBhc3NvdXQgeWVhclxuICBjb25zdCBnZXRGaWx0ZXJlZFN0dWRlbnRzID0gKCkgPT4ge1xuICAgIGxldCBmaWx0ZXJlZCA9IGFsbFN0dWRlbnRzO1xuICAgIGlmIChzZWxlY3RlZERlcGFydG1lbnQpIHtcbiAgICAgIGZpbHRlcmVkID0gZmlsdGVyZWQuZmlsdGVyKHMgPT4gcy5kZXBhcnRtZW50ID09PSBzZWxlY3RlZERlcGFydG1lbnQpO1xuICAgIH1cbiAgICBpZiAoc2VsZWN0ZWRQYXNzb3V0WWVhcikge1xuICAgICAgZmlsdGVyZWQgPSBmaWx0ZXJlZC5maWx0ZXIocyA9PiB7XG4gICAgICAgIGlmICghcy55ZWFyIHx8IHMueWVhciA9PT0gJ04vQScpIHJldHVybiBmYWxzZTtcbiAgICAgICAgY29uc3QgcGFydHMgPSBzLnllYXIuc3BsaXQoJy0nKTtcbiAgICAgICAgcmV0dXJuIHBhcnRzLmxlbmd0aCA9PT0gMiAmJiBwYXJ0c1sxXSA9PT0gU3RyaW5nKHNlbGVjdGVkUGFzc291dFllYXIpO1xuICAgICAgfSk7XG4gICAgfVxuICAgIGlmIChkZWJvdW5jZWRTZWFyY2hUZXJtKSB7XG4gICAgICBjb25zdCBzZWFyY2hMb3dlciA9IGRlYm91bmNlZFNlYXJjaFRlcm0udG9Mb3dlckNhc2UoKTtcbiAgICAgIGZpbHRlcmVkID0gZmlsdGVyZWQuZmlsdGVyKHN0dWRlbnQgPT4gXG4gICAgICAgIHN0dWRlbnQubmFtZS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaExvd2VyKSB8fFxuICAgICAgICBzdHVkZW50LnJvbGxOdW1iZXIudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hMb3dlcilcbiAgICAgICk7XG4gICAgfVxuICAgIC8vIENHUEEgZmlsdGVyXG4gICAgZmlsdGVyZWQgPSBmaWx0ZXJlZC5maWx0ZXIoc3R1ZGVudCA9PiB7XG4gICAgICBjb25zdCBjZ3BhID0gcGFyc2VGbG9hdChzdHVkZW50LmNncGEpO1xuICAgICAgaWYgKGNncGFNaW4gJiYgKGlzTmFOKGNncGEpIHx8IGNncGEgPCBwYXJzZUZsb2F0KGNncGFNaW4pKSkgcmV0dXJuIGZhbHNlO1xuICAgICAgaWYgKGNncGFNYXggJiYgKGlzTmFOKGNncGEpIHx8IGNncGEgPiBwYXJzZUZsb2F0KGNncGFNYXgpKSkgcmV0dXJuIGZhbHNlO1xuICAgICAgcmV0dXJuIHRydWU7XG4gICAgfSk7XG4gICAgcmV0dXJuIGZpbHRlcmVkO1xuICB9O1xuXG4gIC8vIFVwZGF0ZSBmaWx0ZXJzIGFuZCByZWZldGNoIHdoZW4gZGVwZW5kZW5jaWVzIGNoYW5nZVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGZldGNoU3R1ZGVudHMoMSk7IC8vIFJlc2V0IHRvIHBhZ2UgMSB3aGVuIGZpbHRlcnMgY2hhbmdlXG4gIH0sIFtzZWxlY3RlZERlcGFydG1lbnQsIHNlbGVjdGVkWWVhciwgZGVib3VuY2VkU2VhcmNoVGVybSwgc2VsZWN0ZWRQYXNzb3V0WWVhciwgY2dwYU1pbiwgY2dwYU1heCwgcGFnZVNpemVdKTtcblxuICAvLyBGaWx0ZXIgc3R1ZGVudHMgYmFzZWQgb24gc2VsZWN0ZWQgZGVwYXJ0bWVudCwgeWVhciwgYW5kIHNlYXJjaCB0ZXJtXG4gIGNvbnN0IGZpbHRlcmVkU3R1ZGVudHMgPSBzdHVkZW50czsgLy8gU3R1ZGVudHMgYXJlIGFscmVhZHkgZmlsdGVyZWQgaW4gZmV0Y2hTdHVkZW50c1xuXG4gIGNvbnN0IGhhbmRsZVN0dWRlbnRDbGljayA9IChzdHVkZW50KSA9PiB7XG4gICAgc2V0U2VsZWN0ZWRTdHVkZW50KHN0dWRlbnQpO1xuICAgIHNldEVkaXRlZFN0dWRlbnQoeyAuLi5zdHVkZW50IH0pO1xuICAgIHNldElzRWRpdGluZyhmYWxzZSk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlQmFja1RvTGlzdCA9ICgpID0+IHtcbiAgICBzZXRTZWxlY3RlZFN0dWRlbnQobnVsbCk7XG4gICAgc2V0SXNFZGl0aW5nKGZhbHNlKTtcbiAgICBzZXRFZGl0ZWRTdHVkZW50KG51bGwpO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUJhY2tUb0RlcGFydG1lbnRzID0gKCkgPT4ge1xuICAgIHNldFNlbGVjdGVkRGVwYXJ0bWVudChudWxsKTtcbiAgICBzZXRTZWxlY3RlZFllYXIoJ2FsbCcpO1xuICAgIHNldFNlYXJjaFRlcm0oJycpO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUVkaXQgPSAoKSA9PiB7XG4gICAgc2V0SXNFZGl0aW5nKHRydWUpO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVNhdmUgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldExvYWRpbmcodHJ1ZSk7XG5cbiAgICAgIC8vIEhlbHBlciBmdW5jdGlvbiB0byBjbGVhbiBkYXRhXG4gICAgICBjb25zdCBjbGVhblZhbHVlID0gKHZhbHVlKSA9PiB7XG4gICAgICAgIGlmICh2YWx1ZSA9PT0gJycgfHwgdmFsdWUgPT09IG51bGwgfHwgdmFsdWUgPT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICB9XG4gICAgICAgIC8vIEhhbmRsZSBzdHJpbmcgdmFsdWVzXG4gICAgICAgIGlmICh0eXBlb2YgdmFsdWUgPT09ICdzdHJpbmcnKSB7XG4gICAgICAgICAgY29uc3QgdHJpbW1lZCA9IHZhbHVlLnRyaW0oKTtcbiAgICAgICAgICByZXR1cm4gdHJpbW1lZCA9PT0gJycgPyBudWxsIDogdHJpbW1lZDtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdmFsdWU7XG4gICAgICB9O1xuXG4gICAgICAvLyBIZWxwZXIgZnVuY3Rpb24gdG8gY2xlYW4gbnVtZXJpYyB2YWx1ZXNcbiAgICAgIGNvbnN0IGNsZWFuTnVtZXJpY1ZhbHVlID0gKHZhbHVlKSA9PiB7XG4gICAgICAgIGlmICh2YWx1ZSA9PT0gJycgfHwgdmFsdWUgPT09IG51bGwgfHwgdmFsdWUgPT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgdmFsdWUgPT09ICdzdHJpbmcnKSB7XG4gICAgICAgICAgY29uc3QgdHJpbW1lZCA9IHZhbHVlLnRyaW0oKTtcbiAgICAgICAgICBpZiAodHJpbW1lZCA9PT0gJycpIHJldHVybiBudWxsO1xuICAgICAgICAgIGNvbnN0IHBhcnNlZCA9IHBhcnNlSW50KHRyaW1tZWQpO1xuICAgICAgICAgIHJldHVybiBpc05hTihwYXJzZWQpID8gbnVsbCA6IHBhcnNlZDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHZhbHVlID09PSAnbnVtYmVyJykge1xuICAgICAgICAgIHJldHVybiBpc05hTih2YWx1ZSkgPyBudWxsIDogdmFsdWU7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICB9O1xuXG4gICAgICAvLyBIZWxwZXIgZnVuY3Rpb24gdG8gY2xlYW4gc3RyaW5nIHZhbHVlcyBzcGVjaWZpY2FsbHlcbiAgICAgIGNvbnN0IGNsZWFuU3RyaW5nVmFsdWUgPSAodmFsdWUpID0+IHtcbiAgICAgICAgaWYgKHZhbHVlID09PSAnJyB8fCB2YWx1ZSA9PT0gbnVsbCB8fCB2YWx1ZSA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgcmV0dXJuICcnO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB0eXBlb2YgdmFsdWUgPT09ICdzdHJpbmcnID8gdmFsdWUudHJpbSgpIDogU3RyaW5nKHZhbHVlKS50cmltKCk7XG4gICAgICB9O1xuXG4gICAgICAvLyBTcGxpdCB0aGUgbmFtZSBwcm9wZXJseVxuICAgICAgY29uc3QgbmFtZVBhcnRzID0gZWRpdGVkU3R1ZGVudC5uYW1lID8gZWRpdGVkU3R1ZGVudC5uYW1lLnRyaW0oKS5zcGxpdCgnICcpIDogW107XG4gICAgICBjb25zdCBmaXJzdE5hbWUgPSBuYW1lUGFydHNbMF0gfHwgJyc7XG4gICAgICBjb25zdCBsYXN0TmFtZSA9IG5hbWVQYXJ0cy5zbGljZSgxKS5qb2luKCcgJykgfHwgJyc7XG5cbiAgICAgIC8vIFByZXBhcmUgdGhlIGRhdGEgZm9yIGJhY2tlbmQgdXBkYXRlXG4gICAgICBjb25zdCB1cGRhdGVEYXRhID0ge1xuICAgICAgICAvLyBCYXNpYyBpbmZvcm1hdGlvbiAtIGVuc3VyZSBzdHJpbmdzIGFyZSBub3QgZW1wdHlcbiAgICAgICAgZmlyc3RfbmFtZTogY2xlYW5TdHJpbmdWYWx1ZShmaXJzdE5hbWUpLFxuICAgICAgICBsYXN0X25hbWU6IGNsZWFuU3RyaW5nVmFsdWUobGFzdE5hbWUpLFxuICAgICAgICBzdHVkZW50X2lkOiBjbGVhblN0cmluZ1ZhbHVlKGVkaXRlZFN0dWRlbnQucm9sbE51bWJlciksXG4gICAgICAgIGNvbnRhY3RfZW1haWw6IGNsZWFuVmFsdWUoZWRpdGVkU3R1ZGVudC5lbWFpbCksXG4gICAgICAgIHBob25lOiBjbGVhblN0cmluZ1ZhbHVlKGVkaXRlZFN0dWRlbnQucGhvbmUpLFxuICAgICAgICBicmFuY2g6IGNsZWFuU3RyaW5nVmFsdWUoZWRpdGVkU3R1ZGVudC5kZXBhcnRtZW50KSxcbiAgICAgICAgZ3BhOiBjbGVhblN0cmluZ1ZhbHVlKGVkaXRlZFN0dWRlbnQuZ3BhKSwgLy8gT3ZlcmFsbCBDR1BBIGFzIHN0cmluZ1xuXG4gICAgICAgIC8vIEFjYWRlbWljIGRldGFpbHMgLSB0aGVzZSBzaG91bGQgYmUgaW50ZWdlcnNcbiAgICAgICAgam9pbmluZ195ZWFyOiBjbGVhbk51bWVyaWNWYWx1ZShlZGl0ZWRTdHVkZW50LmpvaW5pbmdfeWVhciksXG4gICAgICAgIHBhc3NvdXRfeWVhcjogY2xlYW5OdW1lcmljVmFsdWUoZWRpdGVkU3R1ZGVudC5wYXNzb3V0X3llYXIpLFxuXG4gICAgICAgIC8vIFBlcnNvbmFsIGRldGFpbHNcbiAgICAgICAgZGF0ZV9vZl9iaXJ0aDogY2xlYW5WYWx1ZShlZGl0ZWRTdHVkZW50LmRhdGVPZkJpcnRoKSxcbiAgICAgICAgYWRkcmVzczogY2xlYW5TdHJpbmdWYWx1ZShlZGl0ZWRTdHVkZW50LmFkZHJlc3MpLFxuICAgICAgICBjaXR5OiBjbGVhblN0cmluZ1ZhbHVlKGVkaXRlZFN0dWRlbnQuY2l0eSksXG4gICAgICAgIGRpc3RyaWN0OiBjbGVhblN0cmluZ1ZhbHVlKGVkaXRlZFN0dWRlbnQuZGlzdHJpY3QpLFxuICAgICAgICBzdGF0ZTogY2xlYW5TdHJpbmdWYWx1ZShlZGl0ZWRTdHVkZW50LnN0YXRlKSxcbiAgICAgICAgcGluY29kZTogY2xlYW5TdHJpbmdWYWx1ZShlZGl0ZWRTdHVkZW50LnBpbmNvZGUpLFxuICAgICAgICBjb3VudHJ5OiBjbGVhblN0cmluZ1ZhbHVlKGVkaXRlZFN0dWRlbnQuY291bnRyeSksXG4gICAgICAgIHBhcmVudF9jb250YWN0OiBjbGVhblN0cmluZ1ZhbHVlKGVkaXRlZFN0dWRlbnQucGFyZW50Q29udGFjdCksXG4gICAgICAgIGVkdWNhdGlvbjogY2xlYW5TdHJpbmdWYWx1ZShlZGl0ZWRTdHVkZW50LmVkdWNhdGlvbiksXG4gICAgICAgIHNraWxsczogQXJyYXkuaXNBcnJheShlZGl0ZWRTdHVkZW50LnNraWxscykgXG4gICAgICAgICAgPyBlZGl0ZWRTdHVkZW50LnNraWxscy5maWx0ZXIoc2tpbGwgPT4gc2tpbGwgJiYgc2tpbGwudHJpbSgpKS5qb2luKCcsICcpXG4gICAgICAgICAgOiBjbGVhblN0cmluZ1ZhbHVlKGVkaXRlZFN0dWRlbnQuc2tpbGxzKSxcblxuICAgICAgICAvLyBBY2FkZW1pYyBzY29yZXMgLSBhbGwgYXMgc3RyaW5ncyB0byBtYXRjaCBtb2RlbFxuICAgICAgICB0ZW50aF9jZ3BhOiBjbGVhblN0cmluZ1ZhbHVlKGVkaXRlZFN0dWRlbnQudGVudGhfY2dwYSksXG4gICAgICAgIHRlbnRoX3BlcmNlbnRhZ2U6IGNsZWFuU3RyaW5nVmFsdWUoZWRpdGVkU3R1ZGVudC50ZW50aF9wZXJjZW50YWdlKSxcbiAgICAgICAgdGVudGhfYm9hcmQ6IGNsZWFuU3RyaW5nVmFsdWUoZWRpdGVkU3R1ZGVudC50ZW50aF9ib2FyZCksXG4gICAgICAgIHRlbnRoX3NjaG9vbDogY2xlYW5TdHJpbmdWYWx1ZShlZGl0ZWRTdHVkZW50LnRlbnRoX3NjaG9vbCksXG4gICAgICAgIHRlbnRoX3llYXJfb2ZfcGFzc2luZzogY2xlYW5TdHJpbmdWYWx1ZShlZGl0ZWRTdHVkZW50LnRlbnRoX3llYXJfb2ZfcGFzc2luZyksXG4gICAgICAgIHRlbnRoX2xvY2F0aW9uOiBjbGVhblN0cmluZ1ZhbHVlKGVkaXRlZFN0dWRlbnQudGVudGhfbG9jYXRpb24pLFxuICAgICAgICB0ZW50aF9zcGVjaWFsaXphdGlvbjogY2xlYW5TdHJpbmdWYWx1ZShlZGl0ZWRTdHVkZW50LnRlbnRoX3NwZWNpYWxpemF0aW9uKSxcblxuICAgICAgICB0d2VsZnRoX2NncGE6IGNsZWFuU3RyaW5nVmFsdWUoZWRpdGVkU3R1ZGVudC50d2VsZnRoX2NncGEpLFxuICAgICAgICB0d2VsZnRoX3BlcmNlbnRhZ2U6IGNsZWFuU3RyaW5nVmFsdWUoZWRpdGVkU3R1ZGVudC50d2VsZnRoX3BlcmNlbnRhZ2UpLFxuICAgICAgICB0d2VsZnRoX2JvYXJkOiBjbGVhblN0cmluZ1ZhbHVlKGVkaXRlZFN0dWRlbnQudHdlbGZ0aF9ib2FyZCksXG4gICAgICAgIHR3ZWxmdGhfc2Nob29sOiBjbGVhblN0cmluZ1ZhbHVlKGVkaXRlZFN0dWRlbnQudHdlbGZ0aF9zY2hvb2wpLFxuICAgICAgICB0d2VsZnRoX3llYXJfb2ZfcGFzc2luZzogY2xlYW5TdHJpbmdWYWx1ZShlZGl0ZWRTdHVkZW50LnR3ZWxmdGhfeWVhcl9vZl9wYXNzaW5nKSxcbiAgICAgICAgdHdlbGZ0aF9sb2NhdGlvbjogY2xlYW5TdHJpbmdWYWx1ZShlZGl0ZWRTdHVkZW50LnR3ZWxmdGhfbG9jYXRpb24pLFxuICAgICAgICB0d2VsZnRoX3NwZWNpYWxpemF0aW9uOiBjbGVhblN0cmluZ1ZhbHVlKGVkaXRlZFN0dWRlbnQudHdlbGZ0aF9zcGVjaWFsaXphdGlvbiksXG4gICAgICB9O1xuXG4gICAgICAvLyBBZGQgc2VtZXN0ZXIgQ0dQQXMgaWYgdGhleSBleGlzdFxuICAgICAgaWYgKGVkaXRlZFN0dWRlbnQuc2VtZXN0ZXJfY2dwYXMgJiYgQXJyYXkuaXNBcnJheShlZGl0ZWRTdHVkZW50LnNlbWVzdGVyX2NncGFzKSkge1xuICAgICAgICBlZGl0ZWRTdHVkZW50LnNlbWVzdGVyX2NncGFzLmZvckVhY2goc2VtZXN0ZXJEYXRhID0+IHtcbiAgICAgICAgICBpZiAoc2VtZXN0ZXJEYXRhLnNlbWVzdGVyID49IDEgJiYgc2VtZXN0ZXJEYXRhLnNlbWVzdGVyIDw9IDggJiYgc2VtZXN0ZXJEYXRhLmNncGEpIHtcbiAgICAgICAgICAgIHVwZGF0ZURhdGFbYHNlbWVzdGVyJHtzZW1lc3RlckRhdGEuc2VtZXN0ZXJ9X2NncGFgXSA9IGNsZWFuU3RyaW5nVmFsdWUoc2VtZXN0ZXJEYXRhLmNncGEpO1xuICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgICB9XG5cbiAgICAgIC8vIFJlbW92ZSBlbXB0eSBzdHJpbmcgdmFsdWVzIGJ1dCBrZWVwIG51bGxzIGZvciBwcm9wZXIgZmllbGQgY2xlYXJpbmdcbiAgICAgIGNvbnN0IGNsZWFuZWRVcGRhdGVEYXRhID0gT2JqZWN0LmZyb21FbnRyaWVzKFxuICAgICAgICBPYmplY3QuZW50cmllcyh1cGRhdGVEYXRhKS5maWx0ZXIoKFtrZXksIHZhbHVlXSkgPT4ge1xuICAgICAgICAgIC8vIEtlZXAgbnVsbHMgZm9yIGNsZWFyaW5nIGZpZWxkcywgcmVtb3ZlIGVtcHR5IHN0cmluZ3MgZXhjZXB0IGZvciByZXF1aXJlZCBmaWVsZHNcbiAgICAgICAgICBjb25zdCByZXF1aXJlZEZpZWxkcyA9IFsnZmlyc3RfbmFtZScsICdsYXN0X25hbWUnLCAnc3R1ZGVudF9pZCcsICdncGEnXTtcbiAgICAgICAgICBpZiAocmVxdWlyZWRGaWVsZHMuaW5jbHVkZXMoa2V5KSkge1xuICAgICAgICAgICAgcmV0dXJuIHZhbHVlICE9PSBudWxsICYmIHZhbHVlICE9PSB1bmRlZmluZWQ7XG4gICAgICAgICAgfVxuICAgICAgICAgIHJldHVybiB2YWx1ZSAhPT0gbnVsbCAmJiB2YWx1ZSAhPT0gdW5kZWZpbmVkICYmIHZhbHVlICE9PSAnJztcbiAgICAgICAgfSlcbiAgICAgICk7XG5cbiAgICAgIC8vIEVuc3VyZSByZXF1aXJlZCBmaWVsZHMgaGF2ZSBkZWZhdWx0IHZhbHVlcyBpZiBtaXNzaW5nXG4gICAgICBpZiAoIWNsZWFuZWRVcGRhdGVEYXRhLmZpcnN0X25hbWUpIGNsZWFuZWRVcGRhdGVEYXRhLmZpcnN0X25hbWUgPSAnU3R1ZGVudCc7XG4gICAgICBpZiAoIWNsZWFuZWRVcGRhdGVEYXRhLmxhc3RfbmFtZSkgY2xlYW5lZFVwZGF0ZURhdGEubGFzdF9uYW1lID0gJyc7XG4gICAgICBpZiAoIWNsZWFuZWRVcGRhdGVEYXRhLnN0dWRlbnRfaWQpIGNsZWFuZWRVcGRhdGVEYXRhLnN0dWRlbnRfaWQgPSBgVEVNUF8ke0RhdGUubm93KCl9YDtcbiAgICAgIGlmICghY2xlYW5lZFVwZGF0ZURhdGEuZ3BhKSBjbGVhbmVkVXBkYXRlRGF0YS5ncGEgPSAnMC4wJztcblxuICAgICAgLy8gRGVidWcgbG9nZ2luZ1xuICAgICAgY29uc29sZS5sb2coJ09yaWdpbmFsIGVkaXRlZFN0dWRlbnQ6JywgZWRpdGVkU3R1ZGVudCk7XG4gICAgICBjb25zb2xlLmxvZygnVXBkYXRlIGRhdGEgYmVpbmcgc2VudDonLCBjbGVhbmVkVXBkYXRlRGF0YSk7XG4gICAgICBjb25zb2xlLmxvZygnU3R1ZGVudCBJRDonLCBlZGl0ZWRTdHVkZW50LmlkKTtcblxuICAgICAgLy8gTWFrZSBBUEkgY2FsbCB0byB1cGRhdGUgc3R1ZGVudFxuICAgICAgY29uc3QgdXBkYXRlZFN0dWRlbnQgPSBhd2FpdCBzdHVkZW50c0FQSS51cGRhdGVTdHVkZW50KGVkaXRlZFN0dWRlbnQuaWQsIGNsZWFuZWRVcGRhdGVEYXRhKTtcblxuICAgICAgLy8gVXBkYXRlIHRoZSBzdHVkZW50IGluIHRoZSBsaXN0IHdpdGggdGhlIHJlc3BvbnNlIGRhdGFcbiAgICAgIGNvbnN0IHVwZGF0ZWRTdHVkZW50RGF0YSA9IHtcbiAgICAgICAgLi4uZWRpdGVkU3R1ZGVudCxcbiAgICAgICAgLi4udXBkYXRlZFN0dWRlbnQsXG4gICAgICAgIG5hbWU6IGAke3VwZGF0ZWRTdHVkZW50LmZpcnN0X25hbWUgfHwgJyd9ICR7dXBkYXRlZFN0dWRlbnQubGFzdF9uYW1lIHx8ICcnfWAudHJpbSgpLFxuICAgICAgICByb2xsTnVtYmVyOiB1cGRhdGVkU3R1ZGVudC5zdHVkZW50X2lkLFxuICAgICAgICBlbWFpbDogdXBkYXRlZFN0dWRlbnQuY29udGFjdF9lbWFpbCxcbiAgICAgICAgZGVwYXJ0bWVudDogdXBkYXRlZFN0dWRlbnQuYnJhbmNoLFxuICAgICAgICBncGE6IHVwZGF0ZWRTdHVkZW50LmdwYSxcbiAgICAgIH07XG5cbiAgICAgIHNldFN0dWRlbnRzKHByZXYgPT5cbiAgICAgICAgcHJldi5tYXAoc3R1ZGVudCA9PlxuICAgICAgICAgIHN0dWRlbnQuaWQgPT09IGVkaXRlZFN0dWRlbnQuaWQgPyB1cGRhdGVkU3R1ZGVudERhdGEgOiBzdHVkZW50XG4gICAgICAgIClcbiAgICAgICk7XG5cbiAgICAgIHNldFNlbGVjdGVkU3R1ZGVudCh1cGRhdGVkU3R1ZGVudERhdGEpO1xuICAgICAgc2V0SXNFZGl0aW5nKGZhbHNlKTtcblxuICAgICAgLy8gU2hvdyBzdWNjZXNzIG1lc3NhZ2VcbiAgICAgIGFsZXJ0KCdTdHVkZW50IHByb2ZpbGUgdXBkYXRlZCBzdWNjZXNzZnVsbHkhJyk7XG5cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgdXBkYXRpbmcgc3R1ZGVudDonLCBlcnJvcik7XG4gICAgICBcbiAgICAgIC8vIE1vcmUgZGV0YWlsZWQgZXJyb3IgbG9nZ2luZ1xuICAgICAgaWYgKGVycm9yLnJlc3BvbnNlKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHJlc3BvbnNlIHN0YXR1czonLCBlcnJvci5yZXNwb25zZS5zdGF0dXMpO1xuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciByZXNwb25zZSBkYXRhOicsIGVycm9yLnJlc3BvbnNlLmRhdGEpO1xuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciByZXNwb25zZSBoZWFkZXJzOicsIGVycm9yLnJlc3BvbnNlLmhlYWRlcnMpO1xuICAgICAgfVxuICAgICAgXG4gICAgICBsZXQgZXJyb3JNZXNzYWdlID0gJ0ZhaWxlZCB0byB1cGRhdGUgc3R1ZGVudCBwcm9maWxlLiBQbGVhc2UgdHJ5IGFnYWluLic7XG4gICAgICBcbiAgICAgIGlmIChlcnJvci5yZXNwb25zZT8uZGF0YSkge1xuICAgICAgICAvLyBIYW5kbGUgdmFsaWRhdGlvbiBlcnJvcnNcbiAgICAgICAgaWYgKHR5cGVvZiBlcnJvci5yZXNwb25zZS5kYXRhID09PSAnb2JqZWN0Jykge1xuICAgICAgICAgIGNvbnN0IGVycm9yRGV0YWlscyA9IFtdO1xuICAgICAgICAgIGZvciAoY29uc3QgW2ZpZWxkLCBtZXNzYWdlc10gb2YgT2JqZWN0LmVudHJpZXMoZXJyb3IucmVzcG9uc2UuZGF0YSkpIHtcbiAgICAgICAgICAgIGlmIChBcnJheS5pc0FycmF5KG1lc3NhZ2VzKSkge1xuICAgICAgICAgICAgICBlcnJvckRldGFpbHMucHVzaChgJHtmaWVsZH06ICR7bWVzc2FnZXMuam9pbignLCAnKX1gKTtcbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgIGVycm9yRGV0YWlscy5wdXNoKGAke2ZpZWxkfTogJHttZXNzYWdlc31gKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgICAgaWYgKGVycm9yRGV0YWlscy5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgICBlcnJvck1lc3NhZ2UgPSBgVmFsaWRhdGlvbiBlcnJvcnM6XFxuJHtlcnJvckRldGFpbHMuam9pbignXFxuJyl9YDtcbiAgICAgICAgICB9XG4gICAgICAgIH0gZWxzZSBpZiAoZXJyb3IucmVzcG9uc2UuZGF0YS5kZXRhaWwpIHtcbiAgICAgICAgICBlcnJvck1lc3NhZ2UgPSBlcnJvci5yZXNwb25zZS5kYXRhLmRldGFpbDtcbiAgICAgICAgfSBlbHNlIGlmIChlcnJvci5yZXNwb25zZS5kYXRhLm1lc3NhZ2UpIHtcbiAgICAgICAgICBlcnJvck1lc3NhZ2UgPSBlcnJvci5yZXNwb25zZS5kYXRhLm1lc3NhZ2U7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICAgIFxuICAgICAgYWxlcnQoYFVwZGF0ZSBmYWlsZWQ6ICR7ZXJyb3JNZXNzYWdlfWApO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlQ2FuY2VsID0gKCkgPT4ge1xuICAgIHNldEVkaXRlZFN0dWRlbnQoeyAuLi5zZWxlY3RlZFN0dWRlbnQgfSk7XG4gICAgc2V0SXNFZGl0aW5nKGZhbHNlKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVJbnB1dENoYW5nZSA9IChmaWVsZCwgdmFsdWUpID0+IHtcbiAgICBzZXRFZGl0ZWRTdHVkZW50KHByZXYgPT4gKHtcbiAgICAgIC4uLnByZXYsXG4gICAgICBbZmllbGRdOiB2YWx1ZVxuICAgIH0pKTtcbiAgfTtcblxuICAvLyBIYW5kbGUgcmV0cnkgYnV0dG9uIGNsaWNrXG4gIGNvbnN0IGhhbmRsZVJldHJ5ID0gKCkgPT4ge1xuICAgIHNldElzUmV0cnlpbmcodHJ1ZSk7XG4gICAgZmV0Y2hTdHVkZW50cygpO1xuICB9O1xuXG4gIC8vIEhlbHAgZGV2ZWxvcGVycyBmaW5kIHRoZSBjb3JyZWN0IEFQSSBlbmRwb2ludFxuICBjb25zdCBkZWJ1Z0JhY2tlbmQgPSAoKSA9PiB7XG4gICAgd2luZG93Lm9wZW4oJ2h0dHA6Ly9sb2NhbGhvc3Q6ODAwMC9hZG1pbi8nKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVTZWFyY2ggPSAoKSA9PiB7XG4gICAgLy8gRm9yY2UgaW1tZWRpYXRlIHNlYXJjaCB3aXRob3V0IHdhaXRpbmcgZm9yIGRlYm91bmNlXG4gICAgc2V0RGVib3VuY2VkU2VhcmNoVGVybShzZWFyY2hUZXJtKTtcbiAgICBzZXRDdXJyZW50UGFnZSgxKTtcbiAgfTtcblxuICAvLyBIYW5kbGUgcGFnaW5hdGlvblxuICBjb25zdCBoYW5kbGVQYWdlQ2hhbmdlID0gKG5ld1BhZ2UpID0+IHtcbiAgICBpZiAobmV3UGFnZSA+PSAxICYmIG5ld1BhZ2UgPD0gdG90YWxQYWdlcykge1xuICAgICAgZmV0Y2hTdHVkZW50cyhuZXdQYWdlKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gSGFuZGxlIHNlYXJjaCBpbnB1dCBjaGFuZ2VcbiAgY29uc3QgaGFuZGxlU2VhcmNoSW5wdXRDaGFuZ2UgPSAoZSkgPT4ge1xuICAgIHNldFNlYXJjaFRlcm0oZS50YXJnZXQudmFsdWUpO1xuICAgIC8vIERvbid0IHRyaWdnZXIgaW1tZWRpYXRlIHNlYXJjaCwgbGV0IGRlYm91bmNlIGhhbmRsZSBpdFxuICB9O1xuXG4gIC8vIEhhbmRsZSBzZWFyY2ggaW5wdXQga2V5IHByZXNzXG4gIGNvbnN0IGhhbmRsZVNlYXJjaEtleURvd24gPSAoZSkgPT4ge1xuICAgIGlmIChlLmtleSA9PT0gJ0VudGVyJykge1xuICAgICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgaGFuZGxlU2VhcmNoKCk7XG4gICAgfVxuICB9O1xuXG4gIGlmIChsb2FkaW5nKSByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIHAtNiBtbC0yMCBvdmVyZmxvdy15LWF1dG8gaC1mdWxsXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGgtZnVsbFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC0xMiB3LTEyIGJvcmRlci10LTIgYm9yZGVyLWItMiBib3JkZXItYmx1ZS01MDAgbWItNFwiPjwvZGl2PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5Mb2FkaW5nIHN0dWRlbnRzLi4uPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbiAgXG4gIGlmIChlcnJvciAmJiBzdHVkZW50cy5sZW5ndGggPT09IDApIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgcC02IG1sLTIwIG92ZXJmbG93LXktYXV0byBoLWZ1bGxcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgaC1mdWxsXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1yZWQtNTAwIG1iLTQgdGV4dC1jZW50ZXIgbWF4LXctbWRcIj5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtbGcgbWItMlwiPkFjY2VzcyBFcnJvcjwvcD5cbiAgICAgICAgICA8cD57ZXJyb3J9PC9wPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNCB0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgIDxwPlBvc3NpYmxlIHNvbHV0aW9uczo8L3A+XG4gICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwibGlzdC1kaXNjIGxpc3QtaW5zaWRlIG10LTIgdGV4dC1sZWZ0XCI+XG4gICAgICAgICAgICAgIDxsaT5NYWtlIHN1cmUgeW91J3JlIGxvZ2dlZCBpbiB3aXRoIGFkbWluIGNyZWRlbnRpYWxzPC9saT5cbiAgICAgICAgICAgICAgPGxpPkNoZWNrIGlmIHlvdXIgc2Vzc2lvbiBoYXMgZXhwaXJlZDwvbGk+XG4gICAgICAgICAgICAgIDxsaT5WZXJpZnkgRGphbmdvIHNlcnZlciBpcyBydW5uaW5nIG9uIHBvcnQgODAwMDwvbGk+XG4gICAgICAgICAgICAgIDxsaT5FbnN1cmUgcHJvcGVyIHBlcm1pc3Npb25zIGFyZSBzZXQgaW4gRGphbmdvPC9saT5cbiAgICAgICAgICAgIDwvdWw+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTMgbXQtNFwiPlxuICAgICAgICAgIHshZXJyb3IuaW5jbHVkZXMoJ2xvZ2luJykgJiYgKFxuICAgICAgICAgICAgPGJ1dHRvbiBcbiAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlUmV0cnl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIHB4LTQgcHktMiBiZy1ibHVlLTYwMCB0ZXh0LXdoaXRlIHJvdW5kZWQtbGcgaG92ZXI6YmctYmx1ZS03MDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICBkaXNhYmxlZD17aXNSZXRyeWluZ31cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPFJlZnJlc2hDdyBjbGFzc05hbWU9e2B3LTQgaC00ICR7aXNSZXRyeWluZyA/ICdhbmltYXRlLXNwaW4nIDogJyd9YH0gLz5cbiAgICAgICAgICAgICAge2lzUmV0cnlpbmcgPyAnUmV0cnlpbmcuLi4nIDogJ1JldHJ5J31cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICl9XG4gICAgICAgICAgPGJ1dHRvbiBcbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHdpbmRvdy5sb2NhdGlvbi5ocmVmID0gJy9sb2dpbid9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBweC00IHB5LTIgYmctZ3JlZW4tNjAwIHRleHQtd2hpdGUgcm91bmRlZC1sZyBob3ZlcjpiZy1ncmVlbi03MDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxVc2VyIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgICAgR28gdG8gTG9naW5cbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIHAtNiBtbC0yMCBvdmVyZmxvdy15LWF1dG8gaC1mdWxsXCI+XG4gICAgICB7IXNlbGVjdGVkU3R1ZGVudCA/IChcbiAgICAgICAgPD5cbiAgICAgICAgICB7IXNlbGVjdGVkRGVwYXJ0bWVudCA/IChcbiAgICAgICAgICAgIDxEZXBhcnRtZW50Q2FyZHNcbiAgICAgICAgICAgICAgZGVwYXJ0bWVudE9wdGlvbnM9e2RlcGFydG1lbnRPcHRpb25zfVxuICAgICAgICAgICAgICBkZXBhcnRtZW50U3RhdHM9e2RlcGFydG1lbnRTdGF0c31cbiAgICAgICAgICAgICAgYWxsU3R1ZGVudHM9e2FsbFN0dWRlbnRzfVxuICAgICAgICAgICAgICBvblNlbGVjdD17c2V0U2VsZWN0ZWREZXBhcnRtZW50fVxuICAgICAgICAgICAgLz5cbiAgICAgICAgICApIDogIXNlbGVjdGVkUGFzc291dFllYXIgPyAoXG4gICAgICAgICAgICA8UGFzc291dFllYXJDYXJkc1xuICAgICAgICAgICAgICBkZXBhcnRtZW50TGFiZWw9e2RlcGFydG1lbnRPcHRpb25zLmZpbmQoZCA9PiBkLnZhbHVlID09PSBzZWxlY3RlZERlcGFydG1lbnQpPy5sYWJlbH1cbiAgICAgICAgICAgICAgb25CYWNrPXtoYW5kbGVCYWNrVG9EZXBhcnRtZW50c31cbiAgICAgICAgICAgICAgZ2V0QXZhaWxhYmxlUGFzc291dFllYXJzPXtnZXRBdmFpbGFibGVQYXNzb3V0WWVhcnN9XG4gICAgICAgICAgICAgIGFsbFN0dWRlbnRzPXthbGxTdHVkZW50c31cbiAgICAgICAgICAgICAgc2VsZWN0ZWREZXBhcnRtZW50PXtzZWxlY3RlZERlcGFydG1lbnR9XG4gICAgICAgICAgICAgIG9uU2VsZWN0WWVhcj17c2V0U2VsZWN0ZWRQYXNzb3V0WWVhcn1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgKSA6IChcbiAgICAgICAgICAgIDxTdHVkZW50TGlzdFxuICAgICAgICAgICAgICBkZXBhcnRtZW50TGFiZWw9e2RlcGFydG1lbnRPcHRpb25zLmZpbmQoZCA9PiBkLnZhbHVlID09PSBzZWxlY3RlZERlcGFydG1lbnQpPy5sYWJlbH1cbiAgICAgICAgICAgICAgcGFzc291dFllYXI9e3NlbGVjdGVkUGFzc291dFllYXJ9XG4gICAgICAgICAgICAgIG9uQmFjaz17KCkgPT4gc2V0U2VsZWN0ZWRQYXNzb3V0WWVhcihudWxsKX1cbiAgICAgICAgICAgICAgc2VhcmNoVGVybT17c2VhcmNoVGVybX1cbiAgICAgICAgICAgICAgaGFuZGxlU2VhcmNoSW5wdXRDaGFuZ2U9e2hhbmRsZVNlYXJjaElucHV0Q2hhbmdlfVxuICAgICAgICAgICAgICBoYW5kbGVTZWFyY2hLZXlEb3duPXtoYW5kbGVTZWFyY2hLZXlEb3dufVxuICAgICAgICAgICAgICBjZ3BhTWluPXtjZ3BhTWlufVxuICAgICAgICAgICAgICBzZXRDZ3BhTWluPXtzZXRDZ3BhTWlufVxuICAgICAgICAgICAgICBjZ3BhTWF4PXtjZ3BhTWF4fVxuICAgICAgICAgICAgICBzZXRDZ3BhTWF4PXtzZXRDZ3BhTWF4fVxuICAgICAgICAgICAgICBoYW5kbGVTZWFyY2g9e2hhbmRsZVNlYXJjaH1cbiAgICAgICAgICAgICAgZ2V0RmlsdGVyZWRTdHVkZW50cz17Z2V0RmlsdGVyZWRTdHVkZW50c31cbiAgICAgICAgICAgICAgY3VycmVudFBhZ2U9e2N1cnJlbnRQYWdlfVxuICAgICAgICAgICAgICBoYW5kbGVQYWdlQ2hhbmdlPXtoYW5kbGVQYWdlQ2hhbmdlfVxuICAgICAgICAgICAgICBoYW5kbGVTdHVkZW50Q2xpY2s9e2hhbmRsZVN0dWRlbnRDbGlja31cbiAgICAgICAgICAgICAgbG9hZGluZz17bG9hZGluZ31cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC8+XG4gICAgICApIDogKFxuICAgICAgICA8U3R1ZGVudFByb2ZpbGVcbiAgICAgICAgICBzZWxlY3RlZFN0dWRlbnQ9e3NlbGVjdGVkU3R1ZGVudH1cbiAgICAgICAgICBlZGl0ZWRTdHVkZW50PXtlZGl0ZWRTdHVkZW50fVxuICAgICAgICAgIGlzRWRpdGluZz17aXNFZGl0aW5nfVxuICAgICAgICAgIGhhbmRsZUJhY2tUb0xpc3Q9e2hhbmRsZUJhY2tUb0xpc3R9XG4gICAgICAgICAgaGFuZGxlRWRpdD17aGFuZGxlRWRpdH1cbiAgICAgICAgICBoYW5kbGVTYXZlPXtoYW5kbGVTYXZlfVxuICAgICAgICAgIGhhbmRsZUNhbmNlbD17aGFuZGxlQ2FuY2VsfVxuICAgICAgICAgIGhhbmRsZUlucHV0Q2hhbmdlPXtoYW5kbGVJbnB1dENoYW5nZX1cbiAgICAgICAgICBkZXBhcnRtZW50T3B0aW9ucz17ZGVwYXJ0bWVudE9wdGlvbnN9XG4gICAgICAgIC8+XG4gICAgICApfVxuICAgIDwvZGl2PlxuICApO1xufSJdLCJuYW1lcyI6WyJBcnJvd0xlZnQiLCJDYWxlbmRhciIsIlJlZnJlc2hDdyIsIlNhdmUiLCJTZWFyY2giLCJVc2VyIiwiWCIsIkdyYWR1YXRpb25DYXAiLCJ1c2VFZmZlY3QiLCJ1c2VSZWYiLCJ1c2VTdGF0ZSIsInN0dWRlbnRzQVBJIiwiZ2V0QXV0aFRva2VuIiwiQ3VzdG9tRHJvcGRvd24iLCJTdHVkZW50UHJvZmlsZSIsIkRlcGFydG1lbnRDYXJkcyIsIlBhc3NvdXRZZWFyQ2FyZHMiLCJTdHVkZW50TGlzdCIsIlN0dWRlbnRNYW5hZ2VtZW50IiwiZGVwYXJ0bWVudE9wdGlvbnMiLCJzZWFyY2hUZXJtIiwic2V0U2VhcmNoVGVybSIsImRlYm91bmNlZFNlYXJjaFRlcm0iLCJzZXREZWJvdW5jZWRTZWFyY2hUZXJtIiwic2VsZWN0ZWREZXBhcnRtZW50Iiwic2V0U2VsZWN0ZWREZXBhcnRtZW50Iiwic2VsZWN0ZWRZZWFyIiwic2V0U2VsZWN0ZWRZZWFyIiwic2VsZWN0ZWRTdHVkZW50Iiwic2V0U2VsZWN0ZWRTdHVkZW50IiwiaXNFZGl0aW5nIiwic2V0SXNFZGl0aW5nIiwiZWRpdGVkU3R1ZGVudCIsInNldEVkaXRlZFN0dWRlbnQiLCJkcm9wZG93blJlZiIsImlzWWVhckRyb3Bkb3duT3BlbiIsInNldElzWWVhckRyb3Bkb3duT3BlbiIsInN0dWRlbnRzIiwic2V0U3R1ZGVudHMiLCJhbGxTdHVkZW50cyIsInNldEFsbFN0dWRlbnRzIiwibG9hZGluZyIsInNldExvYWRpbmciLCJlcnJvciIsInNldEVycm9yIiwiaXNSZXRyeWluZyIsInNldElzUmV0cnlpbmciLCJjdXJyZW50UGFnZSIsInNldEN1cnJlbnRQYWdlIiwidG90YWxQYWdlcyIsInNldFRvdGFsUGFnZXMiLCJ0b3RhbFN0dWRlbnRzIiwic2V0VG90YWxTdHVkZW50cyIsInBhZ2VTaXplIiwic2V0UGFnZVNpemUiLCJhdmFpbGFibGVZZWFycyIsInNldEF2YWlsYWJsZVllYXJzIiwiZGVwYXJ0bWVudFN0YXRzIiwic2V0RGVwYXJ0bWVudFN0YXRzIiwic2VsZWN0ZWRQYXNzb3V0WWVhciIsInNldFNlbGVjdGVkUGFzc291dFllYXIiLCJjZ3BhTWluIiwic2V0Q2dwYU1pbiIsImNncGFNYXgiLCJzZXRDZ3BhTWF4IiwidmFsdWUiLCJsYWJlbCIsInRyYW5zZm9ybVN0dWRlbnREYXRhIiwic3R1ZGVudCIsImlkIiwicm9sbE51bWJlciIsInN0dWRlbnRfaWQiLCJuYW1lIiwiZmlyc3RfbmFtZSIsImxhc3RfbmFtZSIsInRyaW0iLCJlbWFpbCIsImNvbnRhY3RfZW1haWwiLCJwaG9uZSIsImRlcGFydG1lbnQiLCJicmFuY2giLCJ5ZWFyIiwiZ2V0WWVhckZyb21CcmFuY2giLCJjZ3BhIiwiZ3BhIiwiYWRkcmVzcyIsImRhdGVPZkJpcnRoIiwiZGF0ZV9vZl9iaXJ0aCIsInBhcmVudENvbnRhY3QiLCJwYXJlbnRfY29udGFjdCIsImVkdWNhdGlvbiIsInNraWxscyIsImpvaW5pbmdfeWVhciIsImFkbWlzc2lvbl95ZWFyIiwicGFzc291dF95ZWFyIiwiZ3JhZHVhdGlvbl95ZWFyIiwidHdlbGZ0aF9jZ3BhIiwiY2xhc3NfMTJfY2dwYSIsInR3ZWxmdGhfcGVyY2VudGFnZSIsImNsYXNzXzEyX3BlcmNlbnRhZ2UiLCJ0d2VsZnRoX3llYXJfb2ZfcGFzc2luZyIsImNsYXNzXzEyX3llYXIiLCJ0d2VsZnRoX3NjaG9vbCIsImNsYXNzXzEyX3NjaG9vbCIsInR3ZWxmdGhfYm9hcmQiLCJjbGFzc18xMl9ib2FyZCIsInR3ZWxmdGhfbG9jYXRpb24iLCJjbGFzc18xMl9sb2NhdGlvbiIsInR3ZWxmdGhfc3BlY2lhbGl6YXRpb24iLCJjbGFzc18xMl9zdHJlYW0iLCJ0ZW50aF9jZ3BhIiwiY2xhc3NfMTBfY2dwYSIsInRlbnRoX3BlcmNlbnRhZ2UiLCJjbGFzc18xMF9wZXJjZW50YWdlIiwidGVudGhfeWVhcl9vZl9wYXNzaW5nIiwiY2xhc3NfMTBfeWVhciIsInRlbnRoX3NjaG9vbCIsImNsYXNzXzEwX3NjaG9vbCIsInRlbnRoX2JvYXJkIiwiY2xhc3NfMTBfYm9hcmQiLCJ0ZW50aF9sb2NhdGlvbiIsImNsYXNzXzEwX2xvY2F0aW9uIiwidGVudGhfc3BlY2lhbGl6YXRpb24iLCJjbGFzc18xMF9zdHJlYW0iLCJjaXR5IiwiZGlzdHJpY3QiLCJzdGF0ZSIsInBpbmNvZGUiLCJwaW5fY29kZSIsImNvdW50cnkiLCJ0ZW50aF9jZXJ0aWZpY2F0ZSIsImNsYXNzXzEwX2NlcnRpZmljYXRlIiwidHdlbGZ0aF9jZXJ0aWZpY2F0ZSIsImNsYXNzXzEyX2NlcnRpZmljYXRlIiwidGVudGhfY2VydGlmaWNhdGVfdXJsIiwiY2xhc3NfMTBfY2VydGlmaWNhdGVfdXJsIiwidHdlbGZ0aF9jZXJ0aWZpY2F0ZV91cmwiLCJjbGFzc18xMl9jZXJ0aWZpY2F0ZV91cmwiLCJyZXN1bWUiLCJyZXN1bWVfdXJsIiwic2VtZXN0ZXJfY2dwYXMiLCJzZW1lc3Rlcl9tYXJrc2hlZXRzIiwidGltZXIiLCJzZXRUaW1lb3V0IiwiY2xlYXJUaW1lb3V0IiwiZmV0Y2hTdHVkZW50cyIsInBhZ2UiLCJ0b2tlbiIsIkVycm9yIiwicGFyYW1zIiwicGFnZV9zaXplIiwic2VhcmNoIiwiY2dwYV9taW4iLCJjZ3BhX21heCIsInJlc3BvbnNlIiwiZ2V0U3R1ZGVudHMiLCJ0cmFuc2Zvcm1lZFN0dWRlbnRzIiwiZGF0YSIsIm1hcCIsInBhZ2luYXRpb24iLCJ0b3RhbF9wYWdlcyIsInRvdGFsX2NvdW50IiwiZXJyIiwiY29uc29sZSIsInN0YXR1cyIsIm1lc3NhZ2UiLCJpbmNsdWRlcyIsImdldFllYXJGcm9tU3R1ZGVudElkIiwic3R1ZGVudElkIiwibGVuZ3RoIiwieWVhclBhcnQiLCJzdWJzdHJpbmciLCJpc05hTiIsIkRhdGUiLCJnZXRGdWxsWWVhciIsInBhcnNlSW50IiwiaGFuZGxlQ2xpY2tPdXRzaWRlIiwiZXZlbnQiLCJjdXJyZW50IiwiY29udGFpbnMiLCJ0YXJnZXQiLCJkb2N1bWVudCIsImFkZEV2ZW50TGlzdGVuZXIiLCJyZW1vdmVFdmVudExpc3RlbmVyIiwiZ2V0QXZhaWxhYmxlWWVhcnMiLCJzdHVkZW50c0RhdGEiLCJ5ZWFycyIsIlNldCIsImZpbHRlciIsInNvcnQiLCJnZXREZXBhcnRtZW50U3RhdHMiLCJzdGF0cyIsImZvckVhY2giLCJPYmplY3QiLCJlbnRyaWVzIiwiY291bnQiLCJnZXRBdmFpbGFibGVQYXNzb3V0WWVhcnMiLCJzIiwicGFydHMiLCJzcGxpdCIsInkiLCJOdW1iZXIiLCJBcnJheSIsImZyb20iLCJhIiwiYiIsImdldEZpbHRlcmVkU3R1ZGVudHMiLCJmaWx0ZXJlZCIsIlN0cmluZyIsInNlYXJjaExvd2VyIiwidG9Mb3dlckNhc2UiLCJwYXJzZUZsb2F0IiwiZmlsdGVyZWRTdHVkZW50cyIsImhhbmRsZVN0dWRlbnRDbGljayIsImhhbmRsZUJhY2tUb0xpc3QiLCJoYW5kbGVCYWNrVG9EZXBhcnRtZW50cyIsImhhbmRsZUVkaXQiLCJoYW5kbGVTYXZlIiwiY2xlYW5WYWx1ZSIsInVuZGVmaW5lZCIsInRyaW1tZWQiLCJjbGVhbk51bWVyaWNWYWx1ZSIsInBhcnNlZCIsImNsZWFuU3RyaW5nVmFsdWUiLCJuYW1lUGFydHMiLCJmaXJzdE5hbWUiLCJsYXN0TmFtZSIsInNsaWNlIiwiam9pbiIsInVwZGF0ZURhdGEiLCJpc0FycmF5Iiwic2tpbGwiLCJzZW1lc3RlckRhdGEiLCJzZW1lc3RlciIsImNsZWFuZWRVcGRhdGVEYXRhIiwiZnJvbUVudHJpZXMiLCJrZXkiLCJyZXF1aXJlZEZpZWxkcyIsIm5vdyIsImxvZyIsInVwZGF0ZWRTdHVkZW50IiwidXBkYXRlU3R1ZGVudCIsInVwZGF0ZWRTdHVkZW50RGF0YSIsInByZXYiLCJhbGVydCIsImhlYWRlcnMiLCJlcnJvck1lc3NhZ2UiLCJlcnJvckRldGFpbHMiLCJmaWVsZCIsIm1lc3NhZ2VzIiwicHVzaCIsImRldGFpbCIsImhhbmRsZUNhbmNlbCIsImhhbmRsZUlucHV0Q2hhbmdlIiwiaGFuZGxlUmV0cnkiLCJkZWJ1Z0JhY2tlbmQiLCJ3aW5kb3ciLCJvcGVuIiwiaGFuZGxlU2VhcmNoIiwiaGFuZGxlUGFnZUNoYW5nZSIsIm5ld1BhZ2UiLCJoYW5kbGVTZWFyY2hJbnB1dENoYW5nZSIsImUiLCJoYW5kbGVTZWFyY2hLZXlEb3duIiwicHJldmVudERlZmF1bHQiLCJkaXYiLCJjbGFzc05hbWUiLCJwIiwidWwiLCJsaSIsImJ1dHRvbiIsIm9uQ2xpY2siLCJkaXNhYmxlZCIsImxvY2F0aW9uIiwiaHJlZiIsIm9uU2VsZWN0IiwiZGVwYXJ0bWVudExhYmVsIiwiZmluZCIsImQiLCJvbkJhY2siLCJvblNlbGVjdFllYXIiLCJwYXNzb3V0WWVhciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/student-management/page.jsx\n"));

/***/ })

});