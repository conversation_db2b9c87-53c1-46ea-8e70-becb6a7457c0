"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/student-management/page",{

/***/ "(app-pages-browser)/./src/api/optimized.js":
/*!******************************!*\
  !*** ./src/api/optimized.js ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cacheUtils: () => (/* binding */ cacheUtils),\n/* harmony export */   companiesAPI: () => (/* binding */ companiesAPI),\n/* harmony export */   dashboardAPI: () => (/* binding */ dashboardAPI),\n/* harmony export */   fetchPaginatedData: () => (/* binding */ fetchPaginatedData),\n/* harmony export */   jobsAPI: () => (/* binding */ jobsAPI),\n/* harmony export */   paginationUtils: () => (/* binding */ paginationUtils),\n/* harmony export */   studentsAPI: () => (/* binding */ studentsAPI)\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(app-pages-browser)/./src/api/client.js\");\n/**\n * Optimized API service for server-side pagination and filtering\n * Replaces inefficient client-side data loading patterns\n */ \n/**\n * Generic function for paginated API calls\n * @param {string} endpoint - API endpoint\n * @param {Object} params - Query parameters\n * @returns {Promise} API response\n */ async function fetchPaginatedData(endpoint) {\n    let params = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    try {\n        const queryParams = new URLSearchParams();\n        // Add pagination parameters\n        if (params.page) queryParams.append('page', params.page);\n        if (params.page_size) queryParams.append('page_size', params.page_size);\n        // Add filter parameters\n        Object.keys(params).forEach((key)=>{\n            if (key !== 'page' && key !== 'page_size' && params[key]) {\n                queryParams.append(key, params[key]);\n            }\n        });\n        const url = \"\".concat(endpoint, \"?\").concat(queryParams.toString());\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url);\n        return {\n            success: true,\n            data: response.data.results || response.data.data || response.data,\n            pagination: response.data.pagination || {\n                page: params.page || 1,\n                page_size: params.page_size || 20,\n                total_count: response.data.total_count || 0,\n                total_pages: response.data.total_pages || 1,\n                has_next: response.data.has_next || false,\n                has_previous: response.data.has_previous || false\n            },\n            metadata: response.data.metadata || {}\n        };\n    } catch (error) {\n        console.error(\"Error fetching paginated data from \".concat(endpoint, \":\"), error);\n        throw error;\n    }\n}\n/**\n * Optimized student API functions\n */ const studentsAPI = {\n    /**\n   * Fetch students with server-side pagination and filtering\n   * @param {Object} params - Query parameters\n   * @returns {Promise} Students data with pagination\n   */ async getStudents () {\n        let params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        return fetchPaginatedData('/api/v1/accounts/students/optimized/', params);\n    },\n    /**\n   * Get student statistics\n   * @param {boolean} forceRefresh - Force refresh cache\n   * @returns {Promise} Student statistics\n   */ async getStudentStats () {\n        let forceRefresh = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        try {\n            const params = forceRefresh ? {\n                refresh: 'true'\n            } : {};\n            const response = await _client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get('/api/v1/metrics/cached/', {\n                params: {\n                    type: 'student_stats',\n                    ...params\n                }\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Error fetching student stats:', error);\n            throw error;\n        }\n    },\n    /**\n   * Get department statistics\n   * @param {boolean} forceRefresh - Force refresh cache\n   * @returns {Promise} Department statistics\n   */ async getDepartmentStats () {\n        let forceRefresh = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        try {\n            const params = forceRefresh ? {\n                refresh: 'true'\n            } : {};\n            const response = await _client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get('/api/v1/metrics/cached/', {\n                params: {\n                    type: 'department_stats',\n                    ...params\n                }\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Error fetching department stats:', error);\n            throw error;\n        }\n    },\n    /**\n   * Search students with optimized backend search\n   * @param {string} searchTerm - Search term\n   * @param {Object} filters - Additional filters\n   * @param {number} page - Page number\n   * @param {number} pageSize - Page size\n   * @returns {Promise} Search results\n   */ async searchStudents (searchTerm) {\n        let filters = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, page = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 1, pageSize = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 20;\n        const params = {\n            search: searchTerm,\n            page,\n            page_size: pageSize,\n            ...filters\n        };\n        return this.getStudents(params);\n    }\n};\n/**\n * Optimized company API functions\n */ const companiesAPI = {\n    /**\n   * Fetch companies with server-side pagination and filtering\n   * @param {Object} params - Query parameters\n   * @returns {Promise} Companies data with pagination\n   */ async getCompanies () {\n        let params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        return fetchPaginatedData('/api/v1/companies/optimized/', params);\n    },\n    /**\n   * Get company statistics\n   * @param {boolean} forceRefresh - Force refresh cache\n   * @returns {Promise} Company statistics\n   */ async getCompanyStats () {\n        let forceRefresh = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        try {\n            const params = forceRefresh ? {\n                refresh: 'true'\n            } : {};\n            const response = await _client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get('/api/v1/metrics/cached/', {\n                params: {\n                    type: 'company_stats',\n                    ...params\n                }\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Error fetching company stats:', error);\n            throw error;\n        }\n    },\n    /**\n   * Search companies with optimized backend search\n   * @param {string} searchTerm - Search term\n   * @param {Object} filters - Additional filters\n   * @param {number} page - Page number\n   * @param {number} pageSize - Page size\n   * @returns {Promise} Search results\n   */ async searchCompanies (searchTerm) {\n        let filters = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, page = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 1, pageSize = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 20;\n        const params = {\n            search: searchTerm,\n            page,\n            page_size: pageSize,\n            ...filters\n        };\n        return this.getCompanies(params);\n    }\n};\n/**\n * Optimized jobs API functions\n */ const jobsAPI = {\n    /**\n   * Fetch jobs with server-side pagination and filtering\n   * @param {Object} params - Query parameters\n   * @returns {Promise} Jobs data with pagination\n   */ async getJobs () {\n        let params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        return fetchPaginatedData('/api/v1/jobs/', params);\n    },\n    /**\n   * Get job statistics\n   * @param {boolean} forceRefresh - Force refresh cache\n   * @returns {Promise} Job statistics\n   */ async getJobStats () {\n        let forceRefresh = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        try {\n            const params = forceRefresh ? {\n                refresh: 'true'\n            } : {};\n            const response = await _client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get('/api/v1/metrics/cached/', {\n                params: {\n                    type: 'job_stats',\n                    ...params\n                }\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Error fetching job stats:', error);\n            throw error;\n        }\n    }\n};\n/**\n * Dashboard API functions\n */ const dashboardAPI = {\n    /**\n   * Get dashboard statistics\n   * @param {boolean} forceRefresh - Force refresh cache\n   * @returns {Promise} Dashboard statistics\n   */ async getDashboardStats () {\n        let forceRefresh = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        try {\n            const params = forceRefresh ? {\n                refresh: 'true'\n            } : {};\n            const response = await _client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get('/api/v1/metrics/cached/', {\n                params: {\n                    type: 'dashboard_stats',\n                    ...params\n                }\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Error fetching dashboard stats:', error);\n            throw error;\n        }\n    },\n    /**\n   * Get placement statistics\n   * @param {boolean} forceRefresh - Force refresh cache\n   * @returns {Promise} Placement statistics\n   */ async getPlacementStats () {\n        let forceRefresh = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        try {\n            const params = forceRefresh ? {\n                refresh: 'true'\n            } : {};\n            const response = await _client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get('/api/v1/metrics/cached/', {\n                params: {\n                    type: 'placement_stats',\n                    ...params\n                }\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Error fetching placement stats:', error);\n            throw error;\n        }\n    }\n};\n/**\n * Utility functions for pagination\n */ const paginationUtils = {\n    /**\n   * Calculate pagination info\n   * @param {number} currentPage - Current page\n   * @param {number} totalPages - Total pages\n   * @param {number} totalCount - Total items\n   * @param {number} pageSize - Items per page\n   * @returns {Object} Pagination info\n   */ calculatePaginationInfo (currentPage, totalPages, totalCount, pageSize) {\n        return {\n            currentPage,\n            totalPages,\n            totalCount,\n            pageSize,\n            startIndex: (currentPage - 1) * pageSize + 1,\n            endIndex: Math.min(currentPage * pageSize, totalCount),\n            hasNext: currentPage < totalPages,\n            hasPrevious: currentPage > 1\n        };\n    },\n    /**\n   * Generate page numbers for pagination component\n   * @param {number} currentPage - Current page\n   * @param {number} totalPages - Total pages\n   * @param {number} maxVisible - Maximum visible page numbers\n   * @returns {Array} Array of page numbers\n   */ generatePageNumbers (currentPage, totalPages) {\n        let maxVisible = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 5;\n        const pages = [];\n        const half = Math.floor(maxVisible / 2);\n        let start = Math.max(1, currentPage - half);\n        let end = Math.min(totalPages, start + maxVisible - 1);\n        // Adjust start if we're near the end\n        if (end - start + 1 < maxVisible) {\n            start = Math.max(1, end - maxVisible + 1);\n        }\n        for(let i = start; i <= end; i++){\n            pages.push(i);\n        }\n        return pages;\n    }\n};\n/**\n * Cache management utilities\n */ const cacheUtils = {\n    /**\n   * Clear all cached data\n   */ clearAllCache () {\n        // Clear localStorage cache\n        const keys = Object.keys(localStorage);\n        keys.forEach((key)=>{\n            if (key.startsWith('cache_') || key.includes('_data') || key.includes('_timestamp')) {\n                localStorage.removeItem(key);\n            }\n        });\n        // Clear sessionStorage cache\n        const sessionKeys = Object.keys(sessionStorage);\n        sessionKeys.forEach((key)=>{\n            if (key.startsWith('cache_') || key.includes('_data') || key.includes('_timestamp')) {\n                sessionStorage.removeItem(key);\n            }\n        });\n    },\n    /**\n   * Check if cached data is still valid\n   * @param {string} key - Cache key\n   * @param {number} maxAge - Maximum age in milliseconds\n   * @returns {boolean} Whether cache is valid\n   */ isCacheValid (key) {\n        let maxAge = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 5 * 60 * 1000;\n        const timestamp = localStorage.getItem(\"\".concat(key, \"_timestamp\"));\n        if (!timestamp) return false;\n        const age = Date.now() - parseInt(timestamp);\n        return age < maxAge;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/api/optimized.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/admin/student-management/page.jsx":
/*!***************************************************!*\
  !*** ./src/app/admin/student-management/page.jsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StudentManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_GraduationCap_RefreshCw_Save_Search_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,GraduationCap,RefreshCw,Save,Search,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_GraduationCap_RefreshCw_Save_Search_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,GraduationCap,RefreshCw,Save,Search,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _api_optimized__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../api/optimized */ \"(app-pages-browser)/./src/api/optimized.js\");\n/* harmony import */ var _utils_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../utils/auth */ \"(app-pages-browser)/./src/utils/auth.js\");\n/* harmony import */ var _StudentDropdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./StudentDropdown */ \"(app-pages-browser)/./src/app/admin/student-management/StudentDropdown.jsx\");\n/* harmony import */ var _StudentProfile__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./StudentProfile */ \"(app-pages-browser)/./src/app/admin/student-management/StudentProfile.jsx\");\n/* harmony import */ var _DepartmentCards__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./DepartmentCards */ \"(app-pages-browser)/./src/app/admin/student-management/DepartmentCards.jsx\");\n/* harmony import */ var _PassoutYearCards__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./PassoutYearCards */ \"(app-pages-browser)/./src/app/admin/student-management/PassoutYearCards.jsx\");\n/* harmony import */ var _StudentList__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./StudentList */ \"(app-pages-browser)/./src/app/admin/student-management/StudentList.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction StudentManagement() {\n    var _departmentOptions_find, _departmentOptions_find1;\n    _s();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [debouncedSearchTerm, setDebouncedSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedDepartment, setSelectedDepartment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedYear, setSelectedYear] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [selectedStudent, setSelectedStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editedStudent, setEditedStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isYearDropdownOpen, setIsYearDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allStudents, setAllStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isRetrying, setIsRetrying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalStudents, setTotalStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [availableYears, setAvailableYears] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [departmentStats, setDepartmentStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedPassoutYear, setSelectedPassoutYear] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [cgpaMin, setCgpaMin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [cgpaMax, setCgpaMax] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Dropdown options\n    const departmentOptions = [\n        {\n            value: 'Computer Science',\n            label: 'Computer Science'\n        },\n        {\n            value: 'Electronics',\n            label: 'Electronics'\n        },\n        {\n            value: 'Mechanical',\n            label: 'Mechanical'\n        },\n        {\n            value: 'Civil',\n            label: 'Civil'\n        },\n        {\n            value: 'Electrical',\n            label: 'Electrical'\n        },\n        {\n            value: 'Information Technology',\n            label: 'Information Technology'\n        },\n        {\n            value: 'Chemical',\n            label: 'Chemical'\n        },\n        {\n            value: 'Biotechnology',\n            label: 'Biotechnology'\n        }\n    ];\n    // Fetch all students for complete dataset\n    const fetchAllStudents = async ()=>{\n        try {\n            const token = (0,_utils_auth__WEBPACK_IMPORTED_MODULE_3__.getAuthToken)();\n            if (!token) {\n                throw new Error('No authentication token found. Please login first.');\n            }\n            let allData = [];\n            let page = 1;\n            let hasMore = true;\n            while(hasMore){\n                const response = await _api_optimized__WEBPACK_IMPORTED_MODULE_2__.studentsAPI.getStudents({\n                    page,\n                    page_size: 100\n                });\n                allData = [\n                    ...allData,\n                    ...response.data\n                ];\n                if (response.pagination) {\n                    hasMore = page < response.pagination.total_pages;\n                    page++;\n                } else {\n                    hasMore = false;\n                }\n            }\n            const studentsData = allData.map((student)=>({\n                    id: student.id,\n                    rollNumber: student.student_id || 'N/A',\n                    name: \"\".concat(student.first_name || '', \" \").concat(student.last_name || '').trim() || 'Unknown',\n                    email: student.contact_email || student.email || 'N/A',\n                    phone: student.phone || 'N/A',\n                    department: student.branch || 'N/A',\n                    year: getYearFromBranch(student.branch, student),\n                    cgpa: student.gpa || 'N/A',\n                    gpa: student.gpa || 'N/A',\n                    address: student.address || 'N/A',\n                    dateOfBirth: student.date_of_birth || '',\n                    parentContact: student.parent_contact || 'N/A',\n                    education: student.education || 'N/A',\n                    skills: student.skills || [],\n                    // Academic details\n                    joining_year: student.joining_year || student.admission_year || '',\n                    passout_year: student.passout_year || student.graduation_year || '',\n                    // Class XII details\n                    twelfth_cgpa: student.twelfth_cgpa || student.class_12_cgpa || '',\n                    twelfth_percentage: student.twelfth_percentage || student.class_12_percentage || '',\n                    twelfth_year_of_passing: student.twelfth_year_of_passing || student.class_12_year || '',\n                    twelfth_school: student.twelfth_school || student.class_12_school || '',\n                    twelfth_board: student.twelfth_board || student.class_12_board || '',\n                    twelfth_location: student.twelfth_location || student.class_12_location || '',\n                    twelfth_specialization: student.twelfth_specialization || student.class_12_stream || '',\n                    // Class X details\n                    tenth_cgpa: student.tenth_cgpa || student.class_10_cgpa || '',\n                    tenth_percentage: student.tenth_percentage || student.class_10_percentage || '',\n                    tenth_year_of_passing: student.tenth_year_of_passing || student.class_10_year || '',\n                    tenth_school: student.tenth_school || student.class_10_school || '',\n                    tenth_board: student.tenth_board || student.class_10_board || '',\n                    tenth_location: student.tenth_location || student.class_10_location || '',\n                    tenth_specialization: student.tenth_specialization || student.class_10_stream || '',\n                    // Address details\n                    city: student.city || '',\n                    district: student.district || '',\n                    state: student.state || '',\n                    pincode: student.pincode || student.pin_code || '',\n                    country: student.country || 'India',\n                    // Certificate URLs\n                    tenth_certificate: student.tenth_certificate || student.class_10_certificate || '',\n                    twelfth_certificate: student.twelfth_certificate || student.class_12_certificate || '',\n                    tenth_certificate_url: student.tenth_certificate_url || student.class_10_certificate_url || '',\n                    twelfth_certificate_url: student.twelfth_certificate_url || student.class_12_certificate_url || '',\n                    // Resume details\n                    resume: student.resume || '',\n                    resume_url: student.resume_url || '',\n                    // Semester-wise CGPA data - use actual backend data\n                    semester_cgpas: student.semester_marksheets || [],\n                    semester_marksheets: student.semester_marksheets || []\n                }));\n            setAllStudents(studentsData);\n            setAvailableYears(getAvailableYears(studentsData));\n            setDepartmentStats(getDepartmentStats(studentsData));\n            return studentsData;\n        } catch (err) {\n            console.error('Error fetching all students:', err);\n            throw err;\n        }\n    };\n    // Debounce search term\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"StudentManagement.useEffect.timer\": ()=>{\n                    setDebouncedSearchTerm(searchTerm);\n                }\n            }[\"StudentManagement.useEffect.timer\"], 300); // 300ms delay\n            return ({\n                \"StudentManagement.useEffect\": ()=>clearTimeout(timer)\n            })[\"StudentManagement.useEffect\"];\n        }\n    }[\"StudentManagement.useEffect\"], [\n        searchTerm\n    ]);\n    // Fetch students from Django backend with pagination\n    const fetchStudents = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1;\n        try {\n            setLoading(true);\n            setError(null);\n            setIsRetrying(false);\n            const token = (0,_utils_auth__WEBPACK_IMPORTED_MODULE_3__.getAuthToken)();\n            if (!token) {\n                throw new Error('No authentication token found. Please login first.');\n            }\n            // Use allStudents if already loaded, otherwise fetch all\n            let allData = allStudents;\n            if (allStudents.length === 0) {\n                allData = await fetchAllStudents();\n            }\n            // Apply filters to get the filtered dataset\n            let filteredData = allData;\n            if (selectedDepartment) {\n                filteredData = filteredData.filter((student)=>student.department === selectedDepartment);\n            }\n            if (selectedYear !== 'all') {\n                filteredData = filteredData.filter((student)=>student.year === selectedYear);\n            }\n            if (debouncedSearchTerm) {\n                const searchLower = debouncedSearchTerm.toLowerCase();\n                filteredData = filteredData.filter((student)=>student.name.toLowerCase().includes(searchLower) || student.rollNumber.toLowerCase().includes(searchLower));\n            }\n            // Implement client-side pagination\n            const studentsPerPage = 10;\n            const startIndex = (page - 1) * studentsPerPage;\n            const endIndex = startIndex + studentsPerPage;\n            const paginatedStudents = filteredData.slice(startIndex, endIndex);\n            setStudents(paginatedStudents);\n            setCurrentPage(page);\n            setTotalPages(Math.ceil(filteredData.length / studentsPerPage));\n            setTotalStudents(filteredData.length);\n            setLoading(false);\n        } catch (err) {\n            var _err_response, _err_response1;\n            console.error('Error fetching students:', err);\n            if (((_err_response = err.response) === null || _err_response === void 0 ? void 0 : _err_response.status) === 401) {\n                setError('Authentication failed. Please login again.');\n            } else if (((_err_response1 = err.response) === null || _err_response1 === void 0 ? void 0 : _err_response1.status) === 403) {\n                setError('You do not have permission to view students. Admin access required.');\n            } else if (err.message.includes('token')) {\n                setError('Please login to access student management.');\n            } else {\n                setError(\"Error: \".concat(err.message));\n            }\n            setStudents([]);\n            setLoading(false);\n        }\n    };\n    // Helper function to determine year from branch (you can customize this logic)\n    const getYearFromBranch = (branch, student)=>{\n        if (student && student.joining_year && student.passout_year) {\n            return \"\".concat(student.joining_year, \"-\").concat(student.passout_year);\n        }\n        return 'N/A';\n    };\n    // Initial data fetch\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            fetchStudents();\n        }\n    }[\"StudentManagement.useEffect\"], []);\n    // Add this useEffect after your existing useEffect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            // Check if user is authenticated\n            const token = (0,_utils_auth__WEBPACK_IMPORTED_MODULE_3__.getAuthToken)();\n            if (!token) {\n                // Redirect to login page or show login prompt\n                setError('Please login to access student management.');\n                setLoading(false);\n                return;\n            }\n            fetchStudents();\n        }\n    }[\"StudentManagement.useEffect\"], []);\n    // Helper function to extract year from student ID (assuming format like CS2021001)\n    const getYearFromStudentId = (studentId)=>{\n        if (studentId && studentId.length >= 6) {\n            const yearPart = studentId.substring(2, 6);\n            if (!isNaN(yearPart)) {\n                return \"\".concat(4 - (new Date().getFullYear() - parseInt(yearPart)), \"th Year\");\n            }\n        }\n        return 'Unknown';\n    };\n    // Close dropdown when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"StudentManagement.useEffect.handleClickOutside\": (event)=>{\n                    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                        setIsYearDropdownOpen(false);\n                    }\n                }\n            }[\"StudentManagement.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"StudentManagement.useEffect\": ()=>{\n                    document.removeEventListener('mousedown', handleClickOutside);\n                }\n            })[\"StudentManagement.useEffect\"];\n        }\n    }[\"StudentManagement.useEffect\"], []);\n    // Get available years from students data\n    const getAvailableYears = (studentsData)=>{\n        const years = [\n            ...new Set(studentsData.map((student)=>student.year).filter((year)=>year && year !== 'N/A'))\n        ];\n        return years.sort();\n    };\n    // Get department statistics\n    const getDepartmentStats = (studentsData)=>{\n        const stats = {};\n        studentsData.forEach((student)=>{\n            if (student.department && student.department !== 'N/A') {\n                stats[student.department] = (stats[student.department] || 0) + 1;\n            }\n        });\n        return Object.entries(stats).map((param)=>{\n            let [department, count] = param;\n            return {\n                department,\n                count\n            };\n        });\n    };\n    // Get available passout years for selected department\n    const getAvailablePassoutYears = ()=>{\n        if (!selectedDepartment) return [];\n        const years = allStudents.filter((s)=>s.department === selectedDepartment && s.year && s.year !== 'N/A').map((s)=>{\n            // Extract passout year from year string (format: \"joining-passout\")\n            const parts = s.year.split('-');\n            return parts.length === 2 ? parts[1] : null;\n        }).filter((y)=>y).map(Number).filter((y)=>!isNaN(y));\n        // Unique and descending\n        return Array.from(new Set(years)).sort((a, b)=>b - a);\n    };\n    // Filter students for selected department and passout year\n    const getFilteredStudents = ()=>{\n        let filtered = allStudents;\n        if (selectedDepartment) {\n            filtered = filtered.filter((s)=>s.department === selectedDepartment);\n        }\n        if (selectedPassoutYear) {\n            filtered = filtered.filter((s)=>{\n                if (!s.year || s.year === 'N/A') return false;\n                const parts = s.year.split('-');\n                return parts.length === 2 && parts[1] === String(selectedPassoutYear);\n            });\n        }\n        if (debouncedSearchTerm) {\n            const searchLower = debouncedSearchTerm.toLowerCase();\n            filtered = filtered.filter((student)=>student.name.toLowerCase().includes(searchLower) || student.rollNumber.toLowerCase().includes(searchLower));\n        }\n        // CGPA filter\n        filtered = filtered.filter((student)=>{\n            const cgpa = parseFloat(student.cgpa);\n            if (cgpaMin && (isNaN(cgpa) || cgpa < parseFloat(cgpaMin))) return false;\n            if (cgpaMax && (isNaN(cgpa) || cgpa > parseFloat(cgpaMax))) return false;\n            return true;\n        });\n        return filtered;\n    };\n    // Update filters and refetch when dependencies change (but not searchTerm)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            if (allStudents.length > 0) {\n                fetchStudents(1); // Reset to page 1 when filters change\n            }\n        }\n    }[\"StudentManagement.useEffect\"], [\n        selectedDepartment,\n        selectedYear,\n        debouncedSearchTerm,\n        selectedPassoutYear\n    ]);\n    // Filter students based on selected department, year, and search term\n    const filteredStudents = students; // Students are already filtered in fetchStudents\n    const handleStudentClick = (student)=>{\n        setSelectedStudent(student);\n        setEditedStudent({\n            ...student\n        });\n        setIsEditing(false);\n    };\n    const handleBackToList = ()=>{\n        setSelectedStudent(null);\n        setIsEditing(false);\n        setEditedStudent(null);\n    };\n    const handleBackToDepartments = ()=>{\n        setSelectedDepartment(null);\n        setSelectedYear('all');\n        setSearchTerm('');\n    };\n    const handleEdit = ()=>{\n        setIsEditing(true);\n    };\n    const handleSave = async ()=>{\n        try {\n            setLoading(true);\n            // Helper function to clean data\n            const cleanValue = (value)=>{\n                if (value === '' || value === null || value === undefined) {\n                    return null;\n                }\n                // Handle string values\n                if (typeof value === 'string') {\n                    const trimmed = value.trim();\n                    return trimmed === '' ? null : trimmed;\n                }\n                return value;\n            };\n            // Helper function to clean numeric values\n            const cleanNumericValue = (value)=>{\n                if (value === '' || value === null || value === undefined) {\n                    return null;\n                }\n                if (typeof value === 'string') {\n                    const trimmed = value.trim();\n                    if (trimmed === '') return null;\n                    const parsed = parseInt(trimmed);\n                    return isNaN(parsed) ? null : parsed;\n                }\n                if (typeof value === 'number') {\n                    return isNaN(value) ? null : value;\n                }\n                return null;\n            };\n            // Helper function to clean string values specifically\n            const cleanStringValue = (value)=>{\n                if (value === '' || value === null || value === undefined) {\n                    return '';\n                }\n                return typeof value === 'string' ? value.trim() : String(value).trim();\n            };\n            // Split the name properly\n            const nameParts = editedStudent.name ? editedStudent.name.trim().split(' ') : [];\n            const firstName = nameParts[0] || '';\n            const lastName = nameParts.slice(1).join(' ') || '';\n            // Prepare the data for backend update\n            const updateData = {\n                // Basic information - ensure strings are not empty\n                first_name: cleanStringValue(firstName),\n                last_name: cleanStringValue(lastName),\n                student_id: cleanStringValue(editedStudent.rollNumber),\n                contact_email: cleanValue(editedStudent.email),\n                phone: cleanStringValue(editedStudent.phone),\n                branch: cleanStringValue(editedStudent.department),\n                gpa: cleanStringValue(editedStudent.gpa),\n                // Academic details - these should be integers\n                joining_year: cleanNumericValue(editedStudent.joining_year),\n                passout_year: cleanNumericValue(editedStudent.passout_year),\n                // Personal details\n                date_of_birth: cleanValue(editedStudent.dateOfBirth),\n                address: cleanStringValue(editedStudent.address),\n                city: cleanStringValue(editedStudent.city),\n                district: cleanStringValue(editedStudent.district),\n                state: cleanStringValue(editedStudent.state),\n                pincode: cleanStringValue(editedStudent.pincode),\n                country: cleanStringValue(editedStudent.country),\n                parent_contact: cleanStringValue(editedStudent.parentContact),\n                education: cleanStringValue(editedStudent.education),\n                skills: Array.isArray(editedStudent.skills) ? editedStudent.skills.filter((skill)=>skill && skill.trim()).join(', ') : cleanStringValue(editedStudent.skills),\n                // Academic scores - all as strings to match model\n                tenth_cgpa: cleanStringValue(editedStudent.tenth_cgpa),\n                tenth_percentage: cleanStringValue(editedStudent.tenth_percentage),\n                tenth_board: cleanStringValue(editedStudent.tenth_board),\n                tenth_school: cleanStringValue(editedStudent.tenth_school),\n                tenth_year_of_passing: cleanStringValue(editedStudent.tenth_year_of_passing),\n                tenth_location: cleanStringValue(editedStudent.tenth_location),\n                tenth_specialization: cleanStringValue(editedStudent.tenth_specialization),\n                twelfth_cgpa: cleanStringValue(editedStudent.twelfth_cgpa),\n                twelfth_percentage: cleanStringValue(editedStudent.twelfth_percentage),\n                twelfth_board: cleanStringValue(editedStudent.twelfth_board),\n                twelfth_school: cleanStringValue(editedStudent.twelfth_school),\n                twelfth_year_of_passing: cleanStringValue(editedStudent.twelfth_year_of_passing),\n                twelfth_location: cleanStringValue(editedStudent.twelfth_location),\n                twelfth_specialization: cleanStringValue(editedStudent.twelfth_specialization)\n            };\n            // Add semester CGPAs if they exist\n            if (editedStudent.semester_cgpas && Array.isArray(editedStudent.semester_cgpas)) {\n                editedStudent.semester_cgpas.forEach((semesterData)=>{\n                    if (semesterData.semester >= 1 && semesterData.semester <= 8 && semesterData.cgpa) {\n                        updateData[\"semester\".concat(semesterData.semester, \"_cgpa\")] = cleanStringValue(semesterData.cgpa);\n                    }\n                });\n            }\n            // Remove empty string values but keep nulls for proper field clearing\n            const cleanedUpdateData = Object.fromEntries(Object.entries(updateData).filter((param)=>{\n                let [key, value] = param;\n                // Keep nulls for clearing fields, remove empty strings except for required fields\n                const requiredFields = [\n                    'first_name',\n                    'last_name',\n                    'student_id',\n                    'gpa'\n                ];\n                if (requiredFields.includes(key)) {\n                    return value !== null && value !== undefined;\n                }\n                return value !== null && value !== undefined && value !== '';\n            }));\n            // Ensure required fields have default values if missing\n            if (!cleanedUpdateData.first_name) cleanedUpdateData.first_name = 'Student';\n            if (!cleanedUpdateData.last_name) cleanedUpdateData.last_name = '';\n            if (!cleanedUpdateData.student_id) cleanedUpdateData.student_id = \"TEMP_\".concat(Date.now());\n            if (!cleanedUpdateData.gpa) cleanedUpdateData.gpa = '0.0';\n            // Debug logging\n            console.log('Original editedStudent:', editedStudent);\n            console.log('Update data being sent:', cleanedUpdateData);\n            console.log('Student ID:', editedStudent.id);\n            // Make API call to update student\n            const updatedStudent = await _api_optimized__WEBPACK_IMPORTED_MODULE_2__.studentsAPI.updateStudent(editedStudent.id, cleanedUpdateData);\n            // Update the student in the list with the response data\n            const updatedStudentData = {\n                ...editedStudent,\n                ...updatedStudent,\n                name: \"\".concat(updatedStudent.first_name || '', \" \").concat(updatedStudent.last_name || '').trim(),\n                rollNumber: updatedStudent.student_id,\n                email: updatedStudent.contact_email,\n                department: updatedStudent.branch,\n                gpa: updatedStudent.gpa\n            };\n            setStudents((prev)=>prev.map((student)=>student.id === editedStudent.id ? updatedStudentData : student));\n            setSelectedStudent(updatedStudentData);\n            setIsEditing(false);\n            // Show success message\n            alert('Student profile updated successfully!');\n        } catch (error) {\n            var _error_response;\n            console.error('Error updating student:', error);\n            // More detailed error logging\n            if (error.response) {\n                console.error('Error response status:', error.response.status);\n                console.error('Error response data:', error.response.data);\n                console.error('Error response headers:', error.response.headers);\n            }\n            let errorMessage = 'Failed to update student profile. Please try again.';\n            if ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) {\n                // Handle validation errors\n                if (typeof error.response.data === 'object') {\n                    const errorDetails = [];\n                    for (const [field, messages] of Object.entries(error.response.data)){\n                        if (Array.isArray(messages)) {\n                            errorDetails.push(\"\".concat(field, \": \").concat(messages.join(', ')));\n                        } else {\n                            errorDetails.push(\"\".concat(field, \": \").concat(messages));\n                        }\n                    }\n                    if (errorDetails.length > 0) {\n                        errorMessage = \"Validation errors:\\n\".concat(errorDetails.join('\\n'));\n                    }\n                } else if (error.response.data.detail) {\n                    errorMessage = error.response.data.detail;\n                } else if (error.response.data.message) {\n                    errorMessage = error.response.data.message;\n                }\n            }\n            alert(\"Update failed: \".concat(errorMessage));\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleCancel = ()=>{\n        setEditedStudent({\n            ...selectedStudent\n        });\n        setIsEditing(false);\n    };\n    const handleInputChange = (field, value)=>{\n        setEditedStudent((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    // Handle retry button click\n    const handleRetry = ()=>{\n        setIsRetrying(true);\n        fetchStudents();\n    };\n    // Help developers find the correct API endpoint\n    const debugBackend = ()=>{\n        window.open('http://localhost:8000/admin/');\n    };\n    const handleSearch = ()=>{\n        // Force immediate search without waiting for debounce\n        setDebouncedSearchTerm(searchTerm);\n        setCurrentPage(1);\n    };\n    // Handle pagination\n    const handlePageChange = (newPage)=>{\n        if (newPage >= 1 && newPage <= totalPages) {\n            fetchStudents(newPage);\n        }\n    };\n    // Handle search input change\n    const handleSearchInputChange = (e)=>{\n        setSearchTerm(e.target.value);\n    // Don't trigger immediate search, let debounce handle it\n    };\n    // Handle search input key press\n    const handleSearchKeyDown = (e)=>{\n        if (e.key === 'Enter') {\n            e.preventDefault();\n            handleSearch();\n        }\n    };\n    if (loading) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 p-6 ml-20 overflow-y-auto h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center h-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mb-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                    lineNumber: 625,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-gray-600\",\n                    children: \"Loading students...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                    lineNumber: 626,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n            lineNumber: 624,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n        lineNumber: 623,\n        columnNumber: 5\n    }, this);\n    if (error && students.length === 0) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 p-6 ml-20 overflow-y-auto h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center h-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-red-500 mb-4 text-center max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-semibold text-lg mb-2\",\n                            children: \"Access Error\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 635,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 636,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 text-sm text-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Possible solutions:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                    lineNumber: 638,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"list-disc list-inside mt-2 text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Make sure you're logged in with admin credentials\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                            lineNumber: 640,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Check if your session has expired\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                            lineNumber: 641,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Verify Django server is running on port 8000\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                            lineNumber: 642,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Ensure proper permissions are set in Django\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                            lineNumber: 643,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                    lineNumber: 639,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 637,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                    lineNumber: 634,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-3 mt-4\",\n                    children: [\n                        !error.includes('login') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleRetry,\n                            className: \"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                            disabled: isRetrying,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_GraduationCap_RefreshCw_Save_Search_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-4 h-4 \".concat(isRetrying ? 'animate-spin' : '')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                    lineNumber: 654,\n                                    columnNumber: 15\n                                }, this),\n                                isRetrying ? 'Retrying...' : 'Retry'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 649,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>window.location.href = '/login',\n                            className: \"flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_GraduationCap_RefreshCw_Save_Search_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                    lineNumber: 662,\n                                    columnNumber: 13\n                                }, this),\n                                \"Go to Login\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 658,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                    lineNumber: 647,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n            lineNumber: 633,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n        lineNumber: 632,\n        columnNumber: 5\n    }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 p-6 ml-20 overflow-y-auto h-full\",\n        children: !selectedStudent ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: !selectedDepartment ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DepartmentCards__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                departmentOptions: departmentOptions,\n                departmentStats: departmentStats,\n                allStudents: allStudents,\n                onSelect: setSelectedDepartment\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                lineNumber: 675,\n                columnNumber: 13\n            }, this) : !selectedPassoutYear ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PassoutYearCards__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                departmentLabel: (_departmentOptions_find = departmentOptions.find((d)=>d.value === selectedDepartment)) === null || _departmentOptions_find === void 0 ? void 0 : _departmentOptions_find.label,\n                onBack: handleBackToDepartments,\n                getAvailablePassoutYears: getAvailablePassoutYears,\n                allStudents: allStudents,\n                selectedDepartment: selectedDepartment,\n                onSelectYear: setSelectedPassoutYear\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                lineNumber: 682,\n                columnNumber: 13\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StudentList__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                departmentLabel: (_departmentOptions_find1 = departmentOptions.find((d)=>d.value === selectedDepartment)) === null || _departmentOptions_find1 === void 0 ? void 0 : _departmentOptions_find1.label,\n                passoutYear: selectedPassoutYear,\n                onBack: ()=>setSelectedPassoutYear(null),\n                searchTerm: searchTerm,\n                handleSearchInputChange: handleSearchInputChange,\n                handleSearchKeyDown: handleSearchKeyDown,\n                cgpaMin: cgpaMin,\n                setCgpaMin: setCgpaMin,\n                cgpaMax: cgpaMax,\n                setCgpaMax: setCgpaMax,\n                handleSearch: handleSearch,\n                getFilteredStudents: getFilteredStudents,\n                currentPage: currentPage,\n                handlePageChange: handlePageChange,\n                handleStudentClick: handleStudentClick,\n                loading: loading\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                lineNumber: 691,\n                columnNumber: 13\n            }, this)\n        }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StudentProfile__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            selectedStudent: selectedStudent,\n            editedStudent: editedStudent,\n            isEditing: isEditing,\n            handleBackToList: handleBackToList,\n            handleEdit: handleEdit,\n            handleSave: handleSave,\n            handleCancel: handleCancel,\n            handleInputChange: handleInputChange,\n            departmentOptions: departmentOptions\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n            lineNumber: 712,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n        lineNumber: 671,\n        columnNumber: 5\n    }, this);\n}\n_s(StudentManagement, \"vV3ywjzqfJaaIG37j/kyv5UkL2w=\");\n_c = StudentManagement;\nvar _c;\n$RefreshReg$(_c, \"StudentManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/student-management/page.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/auth.js":
/*!***************************!*\
  !*** ./src/utils/auth.js ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAuthToken: () => (/* binding */ getAuthToken),\n/* harmony export */   getUserData: () => (/* binding */ getUserData),\n/* harmony export */   getUserId: () => (/* binding */ getUserId),\n/* harmony export */   isLoggedIn: () => (/* binding */ isLoggedIn)\n/* harmony export */ });\n/**\r\n * Utility functions for authentication\r\n */ /**\r\n * Gets the current logged-in user ID\r\n * @returns {string|null} The user ID or null if not logged in\r\n */ function getUserId() {\n    try {\n        // Check for authentication data in localStorage\n        const authToken = localStorage.getItem('access');\n        if (!authToken) {\n            return null;\n        }\n        // Get user data from localStorage\n        const userData = localStorage.getItem('user');\n        if (userData) {\n            const parsedUser = JSON.parse(userData);\n            return parsedUser.id || parsedUser.user_id || null;\n        }\n        // Alternative: try to get from JWT token if user data is not stored separately\n        if (authToken) {\n            try {\n                // Decode JWT token to get user ID\n                // JWT tokens are in format: header.payload.signature\n                const payload = authToken.split('.')[1];\n                const decodedPayload = JSON.parse(atob(payload));\n                return decodedPayload.user_id || decodedPayload.id || decodedPayload.sub || null;\n            } catch (e) {\n                console.error('Error decoding JWT token:', e);\n            }\n        }\n        return null;\n    } catch (error) {\n        console.error('Error getting user ID:', error);\n        return null;\n    }\n}\n/**\r\n * Checks if the user is logged in\r\n * @returns {boolean} True if logged in, false otherwise\r\n */ function isLoggedIn() {\n    const token = localStorage.getItem('access');\n    return !!token;\n}\n/**\r\n * Gets the authentication token\r\n * @returns {string|null} The token or null if not logged in\r\n */ function getAuthToken() {\n    return localStorage.getItem('access');\n}\n/**\r\n * Gets the current user data\r\n * @returns {Object|null} The user data or null if not logged in\r\n */ function getUserData() {\n    try {\n        const userData = localStorage.getItem('user');\n        return userData ? JSON.parse(userData) : null;\n    } catch (error) {\n        console.error('Error getting user data:', error);\n        return null;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/auth.js\n"));

/***/ })

});