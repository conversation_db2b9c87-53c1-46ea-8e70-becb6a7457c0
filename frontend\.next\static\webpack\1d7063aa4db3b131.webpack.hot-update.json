{"c": ["app/layout", "app/admin/student-management/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpgaut%5C%5CDocuments%5C%5CVS%20CODE%5C%5Ccombine%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Cstudent-management%5C%5Cpage.jsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!", "(app-pages-browser)/./src/api/optimized.js", "(app-pages-browser)/./src/app/admin/student-management/DepartmentCards.jsx", "(app-pages-browser)/./src/app/admin/student-management/DocumentsModal.jsx", "(app-pages-browser)/./src/app/admin/student-management/PassoutYearCards.jsx", "(app-pages-browser)/./src/app/admin/student-management/ResumeModal.jsx", "(app-pages-browser)/./src/app/admin/student-management/StudentDropdown.jsx", "(app-pages-browser)/./src/app/admin/student-management/StudentList.jsx", "(app-pages-browser)/./src/app/admin/student-management/StudentProfile.jsx", "(app-pages-browser)/./src/app/admin/student-management/page.jsx"]}