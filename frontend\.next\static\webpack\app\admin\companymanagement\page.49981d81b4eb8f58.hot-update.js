"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/companymanagement/page",{

/***/ "(app-pages-browser)/./src/app/admin/companymanagement/page.jsx":
/*!**************************************************!*\
  !*** ./src/app/admin/companymanagement/page.jsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Employers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Edit,Filter,GraduationCap,Heart,Loader,MapPin,PlusCircle,Search,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Edit,Filter,GraduationCap,Heart,Loader,MapPin,PlusCircle,Search,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Edit,Filter,GraduationCap,Heart,Loader,MapPin,PlusCircle,Search,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Edit,Filter,GraduationCap,Heart,Loader,MapPin,PlusCircle,Search,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Edit,Filter,GraduationCap,Heart,Loader,MapPin,PlusCircle,Search,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Edit,Filter,GraduationCap,Heart,Loader,MapPin,PlusCircle,Search,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Edit,Filter,GraduationCap,Heart,Loader,MapPin,PlusCircle,Search,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Edit,Filter,GraduationCap,Heart,Loader,MapPin,PlusCircle,Search,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Edit,Filter,GraduationCap,Heart,Loader,MapPin,PlusCircle,Search,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Edit,Filter,GraduationCap,Heart,Loader,MapPin,PlusCircle,Search,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Edit,Filter,GraduationCap,Heart,Loader,MapPin,PlusCircle,Search,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _api_optimized__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../api/optimized */ \"(app-pages-browser)/./src/api/optimized.js\");\n/* harmony import */ var _api_companies__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../api/companies */ \"(app-pages-browser)/./src/api/companies.js\");\n/* harmony import */ var _utils_auth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../utils/auth */ \"(app-pages-browser)/./src/utils/auth.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n// Import API services\n\n\n\nfunction Employers() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [companies, setCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [tierFilter, setTierFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('ALL');\n    const [industryFilter, setIndustryFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('ALL');\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('name');\n    const [followedCompanies, setFollowedCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        total: 0,\n        tier1: 0,\n        tier2: 0,\n        tier3: 0,\n        campus_recruiting: 0\n    });\n    // Pagination state\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalCount, setTotalCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(8); // Show 8 cards per page\n    // Fetch companies with server-side pagination and filtering\n    const fetchCompanies = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1;\n        try {\n            setLoading(true);\n            setError(null);\n            // Build filter parameters for server-side filtering\n            const params = {\n                page,\n                page_size: pageSize\n            };\n            // Add search filter\n            if (searchTerm) {\n                params.search = searchTerm;\n            }\n            // Add tier filter\n            if (tierFilter !== 'ALL') {\n                params.tier = tierFilter;\n            }\n            // Add industry filter\n            if (industryFilter !== 'ALL') {\n                params.industry = industryFilter;\n            }\n            // Add sorting\n            if (sortBy) {\n                params.ordering = sortBy === 'name' ? 'name' : sortBy === 'jobs' ? '-total_active_jobs' : sortBy === 'applicants' ? '-total_applicants' : sortBy === 'tier' ? 'tier' : 'name';\n            }\n            // Fetch data from optimized API\n            const response = await _api_optimized__WEBPACK_IMPORTED_MODULE_3__.companiesAPI.getCompanies(params);\n            setCompanies(response.data);\n            setCurrentPage(page);\n            setTotalPages(response.pagination.total_pages);\n            setTotalCount(response.pagination.total_count);\n            setLoading(false);\n        } catch (err) {\n            console.error('Error fetching companies:', err);\n            setError('Failed to load companies. Please try again.');\n            setCompanies([]);\n            setLoading(false);\n        }\n    };\n    // Fetch company statistics\n    const fetchStats = async ()=>{\n        try {\n            const statsResponse = await _api_optimized__WEBPACK_IMPORTED_MODULE_3__.companiesAPI.getCompanyStats();\n            setStats(statsResponse.data || statsResponse);\n        } catch (statsError) {\n            console.error('Error fetching company stats:', statsError);\n            // Fallback stats will be calculated from current page data\n            setStats({\n                total: totalCount,\n                tier1: 0,\n                tier2: 0,\n                tier3: 0,\n                campus_recruiting: 0\n            });\n        }\n    };\n    // Initial data fetch\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Employers.useEffect\": ()=>{\n            fetchCompanies();\n            fetchStats();\n            // Load followed companies from API\n            const userId = (0,_utils_auth__WEBPACK_IMPORTED_MODULE_5__.getUserId)();\n            if (userId) {\n                loadFollowedCompanies(userId);\n            }\n        }\n    }[\"Employers.useEffect\"], []);\n    // Function to load followed companies from API\n    const loadFollowedCompanies = async (userId)=>{\n        try {\n            const response = await (0,_api_companies__WEBPACK_IMPORTED_MODULE_4__.getUserFollowedCompanies)(userId);\n            const followedIds = response.data.map((company)=>company.id);\n            setFollowedCompanies(new Set(followedIds));\n        } catch (error) {\n            console.error('Error loading followed companies:', error);\n        }\n    };\n    // Industries will be fetched from backend or hardcoded for now\n    const industries = [\n        'Technology',\n        'Finance',\n        'Healthcare',\n        'Manufacturing',\n        'Consulting',\n        'E-commerce'\n    ];\n    // Companies are already filtered and sorted on the server side\n    const currentCompanies = companies;\n    // Change page\n    const paginate = (pageNumber)=>setCurrentPage(pageNumber);\n    const nextPage = ()=>setCurrentPage((prev)=>Math.min(prev + 1, totalPages));\n    const prevPage = ()=>setCurrentPage((prev)=>Math.max(prev - 1, 1));\n    // Reset to first page when filters change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Employers.useEffect\": ()=>{\n            setCurrentPage(1);\n        }\n    }[\"Employers.useEffect\"], [\n        searchTerm,\n        tierFilter,\n        industryFilter,\n        sortBy\n    ]);\n    const toggleFollowCompany = async (e, companyId)=>{\n        e.stopPropagation(); // Prevent navigation when clicking follow button\n        try {\n            const userId = (0,_utils_auth__WEBPACK_IMPORTED_MODULE_5__.getUserId)();\n            if (!userId) {\n                alert(\"Please log in to follow companies\");\n                return;\n            }\n            const newFollowedCompanies = new Set(followedCompanies);\n            if (newFollowedCompanies.has(companyId)) {\n                await unfollowCompany(companyId, userId);\n                newFollowedCompanies.delete(companyId);\n            } else {\n                await followCompany(companyId, userId);\n                newFollowedCompanies.add(companyId);\n            }\n            setFollowedCompanies(newFollowedCompanies);\n        } catch (error) {\n            console.error('Error updating follow status:', error);\n            alert('Failed to update follow status. Please try again.');\n        }\n    };\n    const getTierColor = (tier)=>{\n        switch(tier){\n            case 'Tier 1':\n                return 'bg-emerald-100 text-emerald-800 border-emerald-200';\n            case 'Tier 2':\n                return 'bg-blue-100 text-blue-800 border-blue-200';\n            case 'Tier 3':\n                return 'bg-purple-100 text-purple-800 border-purple-200';\n            default:\n                return 'bg-gray-100 text-gray-800 border-gray-200';\n        }\n    };\n    const handleCompanyClick = (companyId)=>{\n        router.push(\"/admin/companymanagement/\".concat(companyId));\n    };\n    const handleCreateCompany = ()=>{\n        router.push('/admin/companymanagement/create');\n    };\n    const handleEditCompany = (e, companyId)=>{\n        e.stopPropagation(); // Prevent navigation to company detail\n        router.push(\"/admin/companymanagement/edit/\".concat(companyId));\n    };\n    const handleDeleteCompany = async (e, companyId)=>{\n        e.stopPropagation(); // Prevent navigation to company detail\n        if (confirm('Are you sure you want to delete this company? This action cannot be undone.')) {\n            try {\n                await (0,_api_companies__WEBPACK_IMPORTED_MODULE_4__.deleteCompany)(companyId);\n                // Remove company from state\n                setAllCompanies(allCompanies.filter((company)=>company.id !== companyId));\n                alert('Company deleted successfully');\n            } catch (error) {\n                console.error('Error deleting company:', error);\n                alert('Failed to delete company. Please try again.');\n            }\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full flex flex-col items-center justify-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative w-16 h-16 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 border-t-4 border-blue-500 rounded-full animate-spin\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                            lineNumber: 236,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-2 border-r-4 border-transparent rounded-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                            lineNumber: 237,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                    lineNumber: 235,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 mb-6\",\n                    children: \"Loading companies...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                    lineNumber: 239,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n            lineNumber: 234,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center max-w-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-500 mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-16 h-16 mx-auto\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                            lineNumber: 249,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                        lineNumber: 248,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-semibold text-gray-900 mb-2\",\n                        children: \"Error Loading Companies\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                        lineNumber: 251,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-4\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                        lineNumber: 252,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>window.location.reload(),\n                        className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                        lineNumber: 253,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                lineNumber: 247,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n            lineNumber: 246,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full overflow-y-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6 flex justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                    children: \"Company Management\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleCreateCompany,\n                                    className: \"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors shadow-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Create New Company\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                            lineNumber: 269,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-5 gap-4 mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg p-4 shadow-sm border border-gray-100\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-blue-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-5 h-5 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: stats.total\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Total Companies\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg p-4 shadow-sm border border-gray-100\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-emerald-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-5 h-5 text-emerald-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: stats.tier1\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Tier 1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg p-4 shadow-sm border border-gray-100\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-blue-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-5 h-5 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: stats.tier2\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 312,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Tier 2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg p-4 shadow-sm border border-gray-100\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-amber-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-5 h-5 text-amber-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: stats.tier3\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Tier 3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg p-4 shadow-sm border border-gray-100\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-amber-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-5 h-5 text-amber-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: followedCompanies.size\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Following\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                            lineNumber: 281,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg p-6 shadow-sm border border-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col md:flex-row gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"Search companies, industries...\",\n                                                    value: searchTerm,\n                                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                                    className: \"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 349,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: tierFilter,\n                                                    onChange: (e)=>setTierFilter(e.target.value),\n                                                    className: \"px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[120px]\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"ALL\",\n                                                            children: \"All Tiers\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                            lineNumber: 365,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"Tier 1\",\n                                                            children: \"Tier 1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                            lineNumber: 366,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"Tier 2\",\n                                                            children: \"Tier 2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"Tier 3\",\n                                                            children: \"Tier 3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                            lineNumber: 368,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: industryFilter,\n                                                    onChange: (e)=>setIndustryFilter(e.target.value),\n                                                    className: \"px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[150px]\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"ALL\",\n                                                            children: \"All Industries\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        industries.map((industry)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: industry,\n                                                                children: industry\n                                                            }, industry, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                                lineNumber: 378,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: sortBy,\n                                                    onChange: (e)=>setSortBy(e.target.value),\n                                                    className: \"px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[130px]\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"name\",\n                                                            children: \"Company A-Z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                            lineNumber: 387,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"jobs\",\n                                                            children: \"Most Jobs\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                            lineNumber: 388,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"applicants\",\n                                                            children: \"Most Popular\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                            lineNumber: 389,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"tier\",\n                                                            children: \"Tier\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                            lineNumber: 390,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 text-sm text-gray-600\",\n                                    children: [\n                                        \"Showing \",\n                                        filteredCompanies.length,\n                                        \" of \",\n                                        allCompanies.length,\n                                        \" companies\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                            lineNumber: 344,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                    lineNumber: 268,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                    children: currentCompanies.length > 0 ? currentCompanies.map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            onClick: ()=>handleCompanyClick(company.id),\n                            className: \"bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-lg transition-all duration-200 cursor-pointer group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-bold text-lg\",\n                                                        children: company.name.charAt(0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 414,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 413,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-gray-900 group-hover:text-blue-600 transition-colors\",\n                                                            children: company.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                            lineNumber: 419,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: company.industry\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                            lineNumber: 422,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 418,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>handleEditCompany(e, company.id),\n                                                    className: \"p-2 rounded-lg transition-colors bg-blue-100 text-blue-600 hover:bg-blue-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 430,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 426,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>handleDeleteCompany(e, company.id),\n                                                    className: \"p-2 rounded-lg transition-colors bg-red-100 text-red-600 hover:bg-red-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 436,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 432,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 411,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 text-sm text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 444,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: company.location\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 445,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 text-sm text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 448,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: company.size\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 447,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 text-sm text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 452,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: company.founded ? \"Founded \".concat(company.founded) : 'Founded year not specified'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 451,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 442,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 py-1 rounded-full text-xs font-medium border \".concat(getTierColor(company.tier)),\n                                            children: company.tier\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 461,\n                                            columnNumber: 19\n                                        }, this),\n                                        company.campus_recruiting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-700 border border-green-200\",\n                                            children: \"Campus Recruiting\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 465,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 460,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 mb-4 line-clamp-3\",\n                                    children: company.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 472,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-3 gap-4 pt-4 border-t border-gray-100\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg font-bold text-gray-900\",\n                                                    children: company.totalActiveJobs\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: \"Active Jobs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 480,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg font-bold text-gray-900\",\n                                                    children: company.totalApplicants\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 483,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: \"Applicants\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 484,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 482,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg font-bold text-gray-900\",\n                                                    children: company.totalHired\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 487,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: \"Hired\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 488,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 486,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 477,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, company.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                            lineNumber: 405,\n                            columnNumber: 15\n                        }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-full flex flex-col items-center justify-center py-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-12 h-12 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 496,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                lineNumber: 495,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-2\",\n                                children: \"No companies found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                lineNumber: 498,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-4\",\n                                children: \"Try adjusting your search or filters\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                lineNumber: 499,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setSearchTerm('');\n                                    setTierFilter('ALL');\n                                    setIndustryFilter('ALL');\n                                    setSortBy('name');\n                                },\n                                className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                children: \"Clear Filters\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                lineNumber: 500,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                        lineNumber: 494,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                    lineNumber: 402,\n                    columnNumber: 9\n                }, this),\n                filteredCompanies.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 flex justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex items-center space-x-2\",\n                        \"aria-label\": \"Pagination\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: prevPage,\n                                disabled: currentPage === 1,\n                                className: \"px-4 py-2 rounded-md border \".concat(currentPage === 1 ? 'text-gray-400 bg-gray-100 cursor-not-allowed' : 'text-gray-700 hover:bg-gray-100'),\n                                children: \"Previous\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                lineNumber: 519,\n                                columnNumber: 15\n                            }, this),\n                            [\n                                ...Array(Math.min(5, totalPages))\n                            ].map((_, i)=>{\n                                // Logic to display pages around current page\n                                let pageNum;\n                                if (totalPages <= 5) {\n                                    pageNum = i + 1;\n                                } else if (currentPage <= 3) {\n                                    pageNum = i + 1;\n                                } else if (currentPage >= totalPages - 2) {\n                                    pageNum = totalPages - 4 + i;\n                                } else {\n                                    pageNum = currentPage - 2 + i;\n                                }\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>paginate(pageNum),\n                                    className: \"w-10 h-10 flex items-center justify-center rounded-md \".concat(currentPage === pageNum ? 'bg-blue-500 text-white' : 'text-gray-700 hover:bg-gray-100'),\n                                    \"aria-current\": currentPage === pageNum ? 'page' : undefined,\n                                    children: pageNum\n                                }, pageNum, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 544,\n                                    columnNumber: 19\n                                }, this);\n                            }),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: nextPage,\n                                disabled: currentPage === totalPages,\n                                className: \"px-4 py-2 rounded-md border \".concat(currentPage === totalPages ? 'text-gray-400 bg-gray-100 cursor-not-allowed' : 'text-gray-700 hover:bg-gray-100'),\n                                children: \"Next\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                lineNumber: 559,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                        lineNumber: 518,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                    lineNumber: 517,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n            lineNumber: 266,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n        lineNumber: 265,\n        columnNumber: 5\n    }, this);\n}\n_s(Employers, \"slan7mGoUv2bTmoFkjvdsGI+mQU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = Employers;\nvar _c;\n$RefreshReg$(_c, \"Employers\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/companymanagement/page.jsx\n"));

/***/ })

});