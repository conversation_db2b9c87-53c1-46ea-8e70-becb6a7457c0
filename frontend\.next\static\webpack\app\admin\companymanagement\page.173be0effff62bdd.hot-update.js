"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/companymanagement/page",{

/***/ "(app-pages-browser)/./src/api/optimized.js":
/*!******************************!*\
  !*** ./src/api/optimized.js ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cacheUtils: () => (/* binding */ cacheUtils),\n/* harmony export */   companiesAPI: () => (/* binding */ companiesAPI),\n/* harmony export */   dashboardAPI: () => (/* binding */ dashboardAPI),\n/* harmony export */   fetchPaginatedData: () => (/* binding */ fetchPaginatedData),\n/* harmony export */   jobsAPI: () => (/* binding */ jobsAPI),\n/* harmony export */   paginationUtils: () => (/* binding */ paginationUtils),\n/* harmony export */   studentsAPI: () => (/* binding */ studentsAPI)\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(app-pages-browser)/./src/api/client.js\");\n/**\n * Optimized API service for server-side pagination and filtering\n * Replaces inefficient client-side data loading patterns\n */ \n/**\n * Generic function for paginated API calls\n * @param {string} endpoint - API endpoint\n * @param {Object} params - Query parameters\n * @returns {Promise} API response\n */ async function fetchPaginatedData(endpoint) {\n    let params = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    try {\n        const queryParams = new URLSearchParams();\n        // Add pagination parameters\n        if (params.page) queryParams.append('page', params.page);\n        if (params.page_size) queryParams.append('page_size', params.page_size);\n        // Add filter parameters\n        Object.keys(params).forEach((key)=>{\n            if (key !== 'page' && key !== 'page_size' && params[key]) {\n                queryParams.append(key, params[key]);\n            }\n        });\n        const url = \"\".concat(endpoint, \"?\").concat(queryParams.toString());\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url);\n        return {\n            success: true,\n            data: response.data.results || response.data.data || response.data,\n            pagination: response.data.pagination || {\n                page: params.page || 1,\n                page_size: params.page_size || 20,\n                total_count: response.data.total_count || 0,\n                total_pages: response.data.total_pages || 1,\n                has_next: response.data.has_next || false,\n                has_previous: response.data.has_previous || false\n            },\n            metadata: response.data.metadata || {}\n        };\n    } catch (error) {\n        console.error(\"Error fetching paginated data from \".concat(endpoint, \":\"), error);\n        throw error;\n    }\n}\n/**\n * Optimized student API functions\n */ const studentsAPI = {\n    /**\n   * Fetch students with server-side pagination and filtering\n   * @param {Object} params - Query parameters\n   * @returns {Promise} Students data with pagination\n   */ async getStudents () {\n        let params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        return fetchPaginatedData('/api/v1/accounts/students/optimized/', params);\n    },\n    /**\n   * Get student statistics\n   * @param {boolean} forceRefresh - Force refresh cache\n   * @returns {Promise} Student statistics\n   */ async getStudentStats () {\n        let forceRefresh = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        try {\n            const params = forceRefresh ? {\n                refresh: 'true'\n            } : {};\n            const response = await _client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get('/api/v1/metrics/cached/', {\n                params: {\n                    type: 'student_stats',\n                    ...params\n                }\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Error fetching student stats:', error);\n            throw error;\n        }\n    },\n    /**\n   * Get department statistics\n   * @param {boolean} forceRefresh - Force refresh cache\n   * @returns {Promise} Department statistics\n   */ async getDepartmentStats () {\n        let forceRefresh = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        try {\n            const params = forceRefresh ? {\n                refresh: 'true'\n            } : {};\n            const response = await _client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get('/api/v1/metrics/cached/', {\n                params: {\n                    type: 'department_stats',\n                    ...params\n                }\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Error fetching department stats:', error);\n            throw error;\n        }\n    },\n    /**\n   * Search students with optimized backend search\n   * @param {string} searchTerm - Search term\n   * @param {Object} filters - Additional filters\n   * @param {number} page - Page number\n   * @param {number} pageSize - Page size\n   * @returns {Promise} Search results\n   */ async searchStudents (searchTerm) {\n        let filters = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, page = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 1, pageSize = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 20;\n        const params = {\n            search: searchTerm,\n            page,\n            page_size: pageSize,\n            ...filters\n        };\n        return this.getStudents(params);\n    }\n};\n/**\n * Optimized company API functions\n */ const companiesAPI = {\n    /**\n   * Fetch companies with server-side pagination and filtering\n   * @param {Object} params - Query parameters\n   * @returns {Promise} Companies data with pagination\n   */ async getCompanies () {\n        let params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        return fetchPaginatedData('/api/v1/companies/optimized/', params);\n    },\n    /**\n   * Get company statistics\n   * @param {boolean} forceRefresh - Force refresh cache\n   * @returns {Promise} Company statistics\n   */ async getCompanyStats () {\n        let forceRefresh = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        try {\n            const params = forceRefresh ? {\n                refresh: 'true'\n            } : {};\n            const response = await _client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get('/api/v1/metrics/cached/', {\n                params: {\n                    type: 'company_stats',\n                    ...params\n                }\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Error fetching company stats:', error);\n            throw error;\n        }\n    },\n    /**\n   * Search companies with optimized backend search\n   * @param {string} searchTerm - Search term\n   * @param {Object} filters - Additional filters\n   * @param {number} page - Page number\n   * @param {number} pageSize - Page size\n   * @returns {Promise} Search results\n   */ async searchCompanies (searchTerm) {\n        let filters = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, page = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 1, pageSize = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 20;\n        const params = {\n            search: searchTerm,\n            page,\n            page_size: pageSize,\n            ...filters\n        };\n        return this.getCompanies(params);\n    }\n};\n/**\n * Optimized jobs API functions\n */ const jobsAPI = {\n    /**\n   * Fetch jobs with server-side pagination and filtering\n   * @param {Object} params - Query parameters\n   * @returns {Promise} Jobs data with pagination\n   */ async getJobs () {\n        let params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        return fetchPaginatedData('/api/v1/jobs/', params);\n    },\n    /**\n   * Get job statistics\n   * @param {boolean} forceRefresh - Force refresh cache\n   * @returns {Promise} Job statistics\n   */ async getJobStats () {\n        let forceRefresh = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        try {\n            const params = forceRefresh ? {\n                refresh: 'true'\n            } : {};\n            const response = await _client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get('/api/v1/metrics/cached/', {\n                params: {\n                    type: 'job_stats',\n                    ...params\n                }\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Error fetching job stats:', error);\n            throw error;\n        }\n    }\n};\n/**\n * Dashboard API functions\n */ const dashboardAPI = {\n    /**\n   * Get dashboard statistics\n   * @param {boolean} forceRefresh - Force refresh cache\n   * @returns {Promise} Dashboard statistics\n   */ async getDashboardStats () {\n        let forceRefresh = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        try {\n            const params = forceRefresh ? {\n                refresh: 'true'\n            } : {};\n            const response = await _client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get('/api/v1/metrics/cached/', {\n                params: {\n                    type: 'dashboard_stats',\n                    ...params\n                }\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Error fetching dashboard stats:', error);\n            throw error;\n        }\n    },\n    /**\n   * Get placement statistics\n   * @param {boolean} forceRefresh - Force refresh cache\n   * @returns {Promise} Placement statistics\n   */ async getPlacementStats () {\n        let forceRefresh = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        try {\n            const params = forceRefresh ? {\n                refresh: 'true'\n            } : {};\n            const response = await _client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get('/api/v1/metrics/cached/', {\n                params: {\n                    type: 'placement_stats',\n                    ...params\n                }\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Error fetching placement stats:', error);\n            throw error;\n        }\n    }\n};\n/**\n * Utility functions for pagination\n */ const paginationUtils = {\n    /**\n   * Calculate pagination info\n   * @param {number} currentPage - Current page\n   * @param {number} totalPages - Total pages\n   * @param {number} totalCount - Total items\n   * @param {number} pageSize - Items per page\n   * @returns {Object} Pagination info\n   */ calculatePaginationInfo (currentPage, totalPages, totalCount, pageSize) {\n        return {\n            currentPage,\n            totalPages,\n            totalCount,\n            pageSize,\n            startIndex: (currentPage - 1) * pageSize + 1,\n            endIndex: Math.min(currentPage * pageSize, totalCount),\n            hasNext: currentPage < totalPages,\n            hasPrevious: currentPage > 1\n        };\n    },\n    /**\n   * Generate page numbers for pagination component\n   * @param {number} currentPage - Current page\n   * @param {number} totalPages - Total pages\n   * @param {number} maxVisible - Maximum visible page numbers\n   * @returns {Array} Array of page numbers\n   */ generatePageNumbers (currentPage, totalPages) {\n        let maxVisible = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 5;\n        const pages = [];\n        const half = Math.floor(maxVisible / 2);\n        let start = Math.max(1, currentPage - half);\n        let end = Math.min(totalPages, start + maxVisible - 1);\n        // Adjust start if we're near the end\n        if (end - start + 1 < maxVisible) {\n            start = Math.max(1, end - maxVisible + 1);\n        }\n        for(let i = start; i <= end; i++){\n            pages.push(i);\n        }\n        return pages;\n    }\n};\n/**\n * Cache management utilities\n */ const cacheUtils = {\n    /**\n   * Clear all cached data\n   */ clearAllCache () {\n        // Clear localStorage cache\n        const keys = Object.keys(localStorage);\n        keys.forEach((key)=>{\n            if (key.startsWith('cache_') || key.includes('_data') || key.includes('_timestamp')) {\n                localStorage.removeItem(key);\n            }\n        });\n        // Clear sessionStorage cache\n        const sessionKeys = Object.keys(sessionStorage);\n        sessionKeys.forEach((key)=>{\n            if (key.startsWith('cache_') || key.includes('_data') || key.includes('_timestamp')) {\n                sessionStorage.removeItem(key);\n            }\n        });\n    },\n    /**\n   * Check if cached data is still valid\n   * @param {string} key - Cache key\n   * @param {number} maxAge - Maximum age in milliseconds\n   * @returns {boolean} Whether cache is valid\n   */ isCacheValid (key) {\n        let maxAge = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 5 * 60 * 1000;\n        const timestamp = localStorage.getItem(\"\".concat(key, \"_timestamp\"));\n        if (!timestamp) return false;\n        const age = Date.now() - parseInt(timestamp);\n        return age < maxAge;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/api/optimized.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/admin/companymanagement/page.jsx":
/*!**************************************************!*\
  !*** ./src/app/admin/companymanagement/page.jsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Employers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Edit,Filter,GraduationCap,Heart,Loader,MapPin,PlusCircle,Search,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Edit,Filter,GraduationCap,Heart,Loader,MapPin,PlusCircle,Search,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Edit,Filter,GraduationCap,Heart,Loader,MapPin,PlusCircle,Search,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Edit,Filter,GraduationCap,Heart,Loader,MapPin,PlusCircle,Search,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Edit,Filter,GraduationCap,Heart,Loader,MapPin,PlusCircle,Search,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Edit,Filter,GraduationCap,Heart,Loader,MapPin,PlusCircle,Search,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Edit,Filter,GraduationCap,Heart,Loader,MapPin,PlusCircle,Search,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Edit,Filter,GraduationCap,Heart,Loader,MapPin,PlusCircle,Search,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Edit,Filter,GraduationCap,Heart,Loader,MapPin,PlusCircle,Search,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Edit,Filter,GraduationCap,Heart,Loader,MapPin,PlusCircle,Search,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Edit,Filter,GraduationCap,Heart,Loader,MapPin,PlusCircle,Search,Star,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _api_optimized__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../api/optimized */ \"(app-pages-browser)/./src/api/optimized.js\");\n/* harmony import */ var _api_companies__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../api/companies */ \"(app-pages-browser)/./src/api/companies.js\");\n/* harmony import */ var _utils_auth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../utils/auth */ \"(app-pages-browser)/./src/utils/auth.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n// Import API services\n\n\n\nfunction Employers() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [allCompanies, setAllCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [tierFilter, setTierFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('ALL');\n    const [industryFilter, setIndustryFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('ALL');\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('name');\n    const [followedCompanies, setFollowedCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        total: 0,\n        tier1: 0,\n        tier2: 0,\n        tier3: 0,\n        campus_recruiting: 0\n    });\n    // Pagination state\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const itemsPerPage = 8; // Show 8 cards per page\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Employers.useEffect\": ()=>{\n            // Load companies from API\n            const loadCompanies = {\n                \"Employers.useEffect.loadCompanies\": async ()=>{\n                    setLoading(true);\n                    try {\n                        // Clear any existing company data to force a fresh fetch\n                        if (true) {\n                            sessionStorage.removeItem('companies_data');\n                            sessionStorage.removeItem('companies_timestamp');\n                        }\n                        console.log('Fetching fresh company data...');\n                        const companies = await fetchCompanies();\n                        console.log(\"Loaded \".concat(companies.length, \" companies\"));\n                        setAllCompanies(companies);\n                        // Also fetch stats\n                        try {\n                            const statsResponse = await getCompanyStats();\n                            setStats(statsResponse.data || statsResponse);\n                        } catch (statsError) {\n                            console.error('Error fetching company stats:', statsError);\n                            // Calculate stats from companies data as fallback\n                            const calculatedStats = {\n                                total: companies.length,\n                                tier1: companies.filter({\n                                    \"Employers.useEffect.loadCompanies\": (c)=>c.tier === 'Tier 1'\n                                }[\"Employers.useEffect.loadCompanies\"]).length,\n                                tier2: companies.filter({\n                                    \"Employers.useEffect.loadCompanies\": (c)=>c.tier === 'Tier 2'\n                                }[\"Employers.useEffect.loadCompanies\"]).length,\n                                tier3: companies.filter({\n                                    \"Employers.useEffect.loadCompanies\": (c)=>c.tier === 'Tier 3'\n                                }[\"Employers.useEffect.loadCompanies\"]).length,\n                                campus_recruiting: companies.filter({\n                                    \"Employers.useEffect.loadCompanies\": (c)=>c.campus_recruiting\n                                }[\"Employers.useEffect.loadCompanies\"]).length\n                            };\n                            setStats(calculatedStats);\n                        }\n                        // Load followed companies from API\n                        const userId = (0,_utils_auth__WEBPACK_IMPORTED_MODULE_5__.getUserId)();\n                        if (userId) {\n                            await loadFollowedCompanies(userId);\n                        }\n                        setError(null);\n                    } catch (err) {\n                        console.error('Error loading companies:', err);\n                        setError('Failed to load companies. Please try again.');\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"Employers.useEffect.loadCompanies\"];\n            loadCompanies();\n        }\n    }[\"Employers.useEffect\"], []);\n    // Function to load followed companies from API\n    const loadFollowedCompanies = async (userId)=>{\n        try {\n            const response = await (0,_api_companies__WEBPACK_IMPORTED_MODULE_4__.getUserFollowedCompanies)(userId);\n            const followedIds = response.data.map((company)=>company.id);\n            setFollowedCompanies(new Set(followedIds));\n        } catch (error) {\n            console.error('Error loading followed companies:', error);\n        }\n    };\n    // Get unique industries for filter\n    const industries = [\n        ...new Set(allCompanies.map((company)=>company.industry))\n    ];\n    // Filter and sort companies\n    const filteredCompanies = allCompanies.filter((company)=>{\n        const matchesSearch = company.name.toLowerCase().includes(searchTerm.toLowerCase()) || company.industry.toLowerCase().includes(searchTerm.toLowerCase()) || company.description.toLowerCase().includes(searchTerm.toLowerCase());\n        const matchesTier = tierFilter === 'ALL' || company.tier === tierFilter;\n        const matchesIndustry = industryFilter === 'ALL' || company.industry === industryFilter;\n        return matchesSearch && matchesTier && matchesIndustry;\n    }).sort((a, b)=>{\n        if (sortBy === 'name') {\n            return a.name.localeCompare(b.name);\n        } else if (sortBy === 'jobs') {\n            return b.totalActiveJobs - a.totalActiveJobs;\n        } else if (sortBy === 'applicants') {\n            return b.totalApplicants - a.totalApplicants;\n        } else if (sortBy === 'tier') {\n            return a.tier.localeCompare(b.tier);\n        }\n        return 0;\n    });\n    // Pagination calculations\n    const totalPages = Math.ceil(filteredCompanies.length / itemsPerPage);\n    const indexOfLastItem = currentPage * itemsPerPage;\n    const indexOfFirstItem = indexOfLastItem - itemsPerPage;\n    const currentCompanies = filteredCompanies.slice(indexOfFirstItem, indexOfLastItem);\n    // Change page\n    const paginate = (pageNumber)=>setCurrentPage(pageNumber);\n    const nextPage = ()=>setCurrentPage((prev)=>Math.min(prev + 1, totalPages));\n    const prevPage = ()=>setCurrentPage((prev)=>Math.max(prev - 1, 1));\n    // Reset to first page when filters change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Employers.useEffect\": ()=>{\n            setCurrentPage(1);\n        }\n    }[\"Employers.useEffect\"], [\n        searchTerm,\n        tierFilter,\n        industryFilter,\n        sortBy\n    ]);\n    const toggleFollowCompany = async (e, companyId)=>{\n        e.stopPropagation(); // Prevent navigation when clicking follow button\n        try {\n            const userId = (0,_utils_auth__WEBPACK_IMPORTED_MODULE_5__.getUserId)();\n            if (!userId) {\n                alert(\"Please log in to follow companies\");\n                return;\n            }\n            const newFollowedCompanies = new Set(followedCompanies);\n            if (newFollowedCompanies.has(companyId)) {\n                await unfollowCompany(companyId, userId);\n                newFollowedCompanies.delete(companyId);\n            } else {\n                await followCompany(companyId, userId);\n                newFollowedCompanies.add(companyId);\n            }\n            setFollowedCompanies(newFollowedCompanies);\n        } catch (error) {\n            console.error('Error updating follow status:', error);\n            alert('Failed to update follow status. Please try again.');\n        }\n    };\n    const getTierColor = (tier)=>{\n        switch(tier){\n            case 'Tier 1':\n                return 'bg-emerald-100 text-emerald-800 border-emerald-200';\n            case 'Tier 2':\n                return 'bg-blue-100 text-blue-800 border-blue-200';\n            case 'Tier 3':\n                return 'bg-purple-100 text-purple-800 border-purple-200';\n            default:\n                return 'bg-gray-100 text-gray-800 border-gray-200';\n        }\n    };\n    const handleCompanyClick = (companyId)=>{\n        router.push(\"/admin/companymanagement/\".concat(companyId));\n    };\n    const handleCreateCompany = ()=>{\n        router.push('/admin/companymanagement/create');\n    };\n    const handleEditCompany = (e, companyId)=>{\n        e.stopPropagation(); // Prevent navigation to company detail\n        router.push(\"/admin/companymanagement/edit/\".concat(companyId));\n    };\n    const handleDeleteCompany = async (e, companyId)=>{\n        e.stopPropagation(); // Prevent navigation to company detail\n        if (confirm('Are you sure you want to delete this company? This action cannot be undone.')) {\n            try {\n                await (0,_api_companies__WEBPACK_IMPORTED_MODULE_4__.deleteCompany)(companyId);\n                // Remove company from state\n                setAllCompanies(allCompanies.filter((company)=>company.id !== companyId));\n                alert('Company deleted successfully');\n            } catch (error) {\n                console.error('Error deleting company:', error);\n                alert('Failed to delete company. Please try again.');\n            }\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full flex flex-col items-center justify-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative w-16 h-16 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 border-t-4 border-blue-500 rounded-full animate-spin\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                            lineNumber: 229,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-2 border-r-4 border-transparent rounded-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                            lineNumber: 230,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                    lineNumber: 228,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 mb-6\",\n                    children: \"Loading companies...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                    lineNumber: 232,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n            lineNumber: 227,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center max-w-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-500 mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-16 h-16 mx-auto\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                            lineNumber: 242,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                        lineNumber: 241,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-semibold text-gray-900 mb-2\",\n                        children: \"Error Loading Companies\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                        lineNumber: 244,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-4\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                        lineNumber: 245,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>window.location.reload(),\n                        className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                        lineNumber: 246,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                lineNumber: 240,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n            lineNumber: 239,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full overflow-y-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6 flex justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                    children: \"Company Management\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleCreateCompany,\n                                    className: \"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors shadow-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Create New Company\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                            lineNumber: 262,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-5 gap-4 mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg p-4 shadow-sm border border-gray-100\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-blue-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-5 h-5 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: stats.total\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Total Companies\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg p-4 shadow-sm border border-gray-100\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-emerald-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-5 h-5 text-emerald-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: stats.tier1\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 293,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Tier 1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg p-4 shadow-sm border border-gray-100\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-blue-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-5 h-5 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: stats.tier2\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 305,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Tier 2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg p-4 shadow-sm border border-gray-100\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-amber-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-5 h-5 text-amber-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: stats.tier3\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Tier 3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg p-4 shadow-sm border border-gray-100\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-amber-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-5 h-5 text-amber-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: followedCompanies.size\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 329,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Following\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                            lineNumber: 274,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg p-6 shadow-sm border border-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col md:flex-row gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"Search companies, industries...\",\n                                                    value: searchTerm,\n                                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                                    className: \"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 342,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: tierFilter,\n                                                    onChange: (e)=>setTierFilter(e.target.value),\n                                                    className: \"px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[120px]\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"ALL\",\n                                                            children: \"All Tiers\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                            lineNumber: 358,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"Tier 1\",\n                                                            children: \"Tier 1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                            lineNumber: 359,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"Tier 2\",\n                                                            children: \"Tier 2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"Tier 3\",\n                                                            children: \"Tier 3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                            lineNumber: 361,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: industryFilter,\n                                                    onChange: (e)=>setIndustryFilter(e.target.value),\n                                                    className: \"px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[150px]\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"ALL\",\n                                                            children: \"All Industries\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                            lineNumber: 369,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        industries.map((industry)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: industry,\n                                                                children: industry\n                                                            }, industry, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                                lineNumber: 371,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: sortBy,\n                                                    onChange: (e)=>setSortBy(e.target.value),\n                                                    className: \"px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[130px]\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"name\",\n                                                            children: \"Company A-Z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"jobs\",\n                                                            children: \"Most Jobs\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                            lineNumber: 381,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"applicants\",\n                                                            children: \"Most Popular\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                            lineNumber: 382,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"tier\",\n                                                            children: \"Tier\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                            lineNumber: 383,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 text-sm text-gray-600\",\n                                    children: [\n                                        \"Showing \",\n                                        filteredCompanies.length,\n                                        \" of \",\n                                        allCompanies.length,\n                                        \" companies\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                            lineNumber: 337,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                    lineNumber: 261,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                    children: currentCompanies.length > 0 ? currentCompanies.map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            onClick: ()=>handleCompanyClick(company.id),\n                            className: \"bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-lg transition-all duration-200 cursor-pointer group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-bold text-lg\",\n                                                        children: company.name.charAt(0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 407,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 406,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-gray-900 group-hover:text-blue-600 transition-colors\",\n                                                            children: company.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                            lineNumber: 412,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: company.industry\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                            lineNumber: 415,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 411,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 405,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>handleEditCompany(e, company.id),\n                                                    className: \"p-2 rounded-lg transition-colors bg-blue-100 text-blue-600 hover:bg-blue-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>handleDeleteCompany(e, company.id),\n                                                    className: \"p-2 rounded-lg transition-colors bg-red-100 text-red-600 hover:bg-red-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 425,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 418,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 404,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 text-sm text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 437,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: company.location\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 438,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 text-sm text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 441,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: company.size\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 442,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 440,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 text-sm text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 445,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: company.founded ? \"Founded \".concat(company.founded) : 'Founded year not specified'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 435,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 py-1 rounded-full text-xs font-medium border \".concat(getTierColor(company.tier)),\n                                            children: company.tier\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 454,\n                                            columnNumber: 19\n                                        }, this),\n                                        company.campus_recruiting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-700 border border-green-200\",\n                                            children: \"Campus Recruiting\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 458,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 453,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 mb-4 line-clamp-3\",\n                                    children: company.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 465,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-3 gap-4 pt-4 border-t border-gray-100\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg font-bold text-gray-900\",\n                                                    children: company.totalActiveJobs\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 472,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: \"Active Jobs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 473,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 471,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg font-bold text-gray-900\",\n                                                    children: company.totalApplicants\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: \"Applicants\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 477,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 475,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg font-bold text-gray-900\",\n                                                    children: company.totalHired\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 480,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: \"Hired\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                                    lineNumber: 481,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                            lineNumber: 479,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 470,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, company.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                            lineNumber: 398,\n                            columnNumber: 15\n                        }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-full flex flex-col items-center justify-center py-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Edit_Filter_GraduationCap_Heart_Loader_MapPin_PlusCircle_Search_Star_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-12 h-12 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 489,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                lineNumber: 488,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-2\",\n                                children: \"No companies found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                lineNumber: 491,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-4\",\n                                children: \"Try adjusting your search or filters\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                lineNumber: 492,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setSearchTerm('');\n                                    setTierFilter('ALL');\n                                    setIndustryFilter('ALL');\n                                    setSortBy('name');\n                                },\n                                className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                children: \"Clear Filters\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                lineNumber: 493,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                        lineNumber: 487,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                    lineNumber: 395,\n                    columnNumber: 9\n                }, this),\n                filteredCompanies.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 flex justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex items-center space-x-2\",\n                        \"aria-label\": \"Pagination\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: prevPage,\n                                disabled: currentPage === 1,\n                                className: \"px-4 py-2 rounded-md border \".concat(currentPage === 1 ? 'text-gray-400 bg-gray-100 cursor-not-allowed' : 'text-gray-700 hover:bg-gray-100'),\n                                children: \"Previous\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                lineNumber: 512,\n                                columnNumber: 15\n                            }, this),\n                            [\n                                ...Array(Math.min(5, totalPages))\n                            ].map((_, i)=>{\n                                // Logic to display pages around current page\n                                let pageNum;\n                                if (totalPages <= 5) {\n                                    pageNum = i + 1;\n                                } else if (currentPage <= 3) {\n                                    pageNum = i + 1;\n                                } else if (currentPage >= totalPages - 2) {\n                                    pageNum = totalPages - 4 + i;\n                                } else {\n                                    pageNum = currentPage - 2 + i;\n                                }\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>paginate(pageNum),\n                                    className: \"w-10 h-10 flex items-center justify-center rounded-md \".concat(currentPage === pageNum ? 'bg-blue-500 text-white' : 'text-gray-700 hover:bg-gray-100'),\n                                    \"aria-current\": currentPage === pageNum ? 'page' : undefined,\n                                    children: pageNum\n                                }, pageNum, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                    lineNumber: 537,\n                                    columnNumber: 19\n                                }, this);\n                            }),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: nextPage,\n                                disabled: currentPage === totalPages,\n                                className: \"px-4 py-2 rounded-md border \".concat(currentPage === totalPages ? 'text-gray-400 bg-gray-100 cursor-not-allowed' : 'text-gray-700 hover:bg-gray-100'),\n                                children: \"Next\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                                lineNumber: 552,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                        lineNumber: 511,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n                    lineNumber: 510,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n            lineNumber: 259,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\",\n        lineNumber: 258,\n        columnNumber: 5\n    }, this);\n}\n_s(Employers, \"WaqKlYCqlV00o1VbxglGA51YEbY=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = Employers;\nvar _c;\n$RefreshReg$(_c, \"Employers\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/companymanagement/page.jsx\n"));

/***/ })

});