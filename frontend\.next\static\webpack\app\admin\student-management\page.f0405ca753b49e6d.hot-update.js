"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/student-management/page",{

/***/ "(app-pages-browser)/./src/app/admin/student-management/page.jsx":
/*!***************************************************!*\
  !*** ./src/app/admin/student-management/page.jsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StudentManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_GraduationCap_RefreshCw_Save_Search_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,GraduationCap,RefreshCw,Save,Search,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_GraduationCap_RefreshCw_Save_Search_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,GraduationCap,RefreshCw,Save,Search,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _api_optimized__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../api/optimized */ \"(app-pages-browser)/./src/api/optimized.js\");\n/* harmony import */ var _utils_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../utils/auth */ \"(app-pages-browser)/./src/utils/auth.js\");\n/* harmony import */ var _StudentDropdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./StudentDropdown */ \"(app-pages-browser)/./src/app/admin/student-management/StudentDropdown.jsx\");\n/* harmony import */ var _StudentProfile__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./StudentProfile */ \"(app-pages-browser)/./src/app/admin/student-management/StudentProfile.jsx\");\n/* harmony import */ var _DepartmentCards__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./DepartmentCards */ \"(app-pages-browser)/./src/app/admin/student-management/DepartmentCards.jsx\");\n/* harmony import */ var _PassoutYearCards__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./PassoutYearCards */ \"(app-pages-browser)/./src/app/admin/student-management/PassoutYearCards.jsx\");\n/* harmony import */ var _StudentList__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./StudentList */ \"(app-pages-browser)/./src/app/admin/student-management/StudentList.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction StudentManagement() {\n    var _departmentOptions_find, _departmentOptions_find1;\n    _s();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [debouncedSearchTerm, setDebouncedSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedDepartment, setSelectedDepartment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedYear, setSelectedYear] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [selectedStudent, setSelectedStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editedStudent, setEditedStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isYearDropdownOpen, setIsYearDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allStudents, setAllStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isRetrying, setIsRetrying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalStudents, setTotalStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(10);\n    const [availableYears, setAvailableYears] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [departmentStats, setDepartmentStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedPassoutYear, setSelectedPassoutYear] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [cgpaMin, setCgpaMin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [cgpaMax, setCgpaMax] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Dropdown options\n    const departmentOptions = [\n        {\n            value: 'Computer Science',\n            label: 'Computer Science'\n        },\n        {\n            value: 'Electronics',\n            label: 'Electronics'\n        },\n        {\n            value: 'Mechanical',\n            label: 'Mechanical'\n        },\n        {\n            value: 'Civil',\n            label: 'Civil'\n        },\n        {\n            value: 'Electrical',\n            label: 'Electrical'\n        },\n        {\n            value: 'Information Technology',\n            label: 'Information Technology'\n        },\n        {\n            value: 'Chemical',\n            label: 'Chemical'\n        },\n        {\n            value: 'Biotechnology',\n            label: 'Biotechnology'\n        }\n    ];\n    // Fetch all students for complete dataset\n    const fetchAllStudents = async ()=>{\n        try {\n            const token = (0,_utils_auth__WEBPACK_IMPORTED_MODULE_3__.getAuthToken)();\n            if (!token) {\n                throw new Error('No authentication token found. Please login first.');\n            }\n            let allData = [];\n            let page = 1;\n            let hasMore = true;\n            while(hasMore){\n                const response = await _api_optimized__WEBPACK_IMPORTED_MODULE_2__.studentsAPI.getStudents({\n                    page,\n                    page_size: 100\n                });\n                allData = [\n                    ...allData,\n                    ...response.data\n                ];\n                if (response.pagination) {\n                    hasMore = page < response.pagination.total_pages;\n                    page++;\n                } else {\n                    hasMore = false;\n                }\n            }\n            const studentsData = allData.map((student)=>({\n                    id: student.id,\n                    rollNumber: student.student_id || 'N/A',\n                    name: \"\".concat(student.first_name || '', \" \").concat(student.last_name || '').trim() || 'Unknown',\n                    email: student.contact_email || student.email || 'N/A',\n                    phone: student.phone || 'N/A',\n                    department: student.branch || 'N/A',\n                    year: getYearFromBranch(student.branch, student),\n                    cgpa: student.gpa || 'N/A',\n                    gpa: student.gpa || 'N/A',\n                    address: student.address || 'N/A',\n                    dateOfBirth: student.date_of_birth || '',\n                    parentContact: student.parent_contact || 'N/A',\n                    education: student.education || 'N/A',\n                    skills: student.skills || [],\n                    // Academic details\n                    joining_year: student.joining_year || student.admission_year || '',\n                    passout_year: student.passout_year || student.graduation_year || '',\n                    // Class XII details\n                    twelfth_cgpa: student.twelfth_cgpa || student.class_12_cgpa || '',\n                    twelfth_percentage: student.twelfth_percentage || student.class_12_percentage || '',\n                    twelfth_year_of_passing: student.twelfth_year_of_passing || student.class_12_year || '',\n                    twelfth_school: student.twelfth_school || student.class_12_school || '',\n                    twelfth_board: student.twelfth_board || student.class_12_board || '',\n                    twelfth_location: student.twelfth_location || student.class_12_location || '',\n                    twelfth_specialization: student.twelfth_specialization || student.class_12_stream || '',\n                    // Class X details\n                    tenth_cgpa: student.tenth_cgpa || student.class_10_cgpa || '',\n                    tenth_percentage: student.tenth_percentage || student.class_10_percentage || '',\n                    tenth_year_of_passing: student.tenth_year_of_passing || student.class_10_year || '',\n                    tenth_school: student.tenth_school || student.class_10_school || '',\n                    tenth_board: student.tenth_board || student.class_10_board || '',\n                    tenth_location: student.tenth_location || student.class_10_location || '',\n                    tenth_specialization: student.tenth_specialization || student.class_10_stream || '',\n                    // Address details\n                    city: student.city || '',\n                    district: student.district || '',\n                    state: student.state || '',\n                    pincode: student.pincode || student.pin_code || '',\n                    country: student.country || 'India',\n                    // Certificate URLs\n                    tenth_certificate: student.tenth_certificate || student.class_10_certificate || '',\n                    twelfth_certificate: student.twelfth_certificate || student.class_12_certificate || '',\n                    tenth_certificate_url: student.tenth_certificate_url || student.class_10_certificate_url || '',\n                    twelfth_certificate_url: student.twelfth_certificate_url || student.class_12_certificate_url || '',\n                    // Resume details\n                    resume: student.resume || '',\n                    resume_url: student.resume_url || '',\n                    // Semester-wise CGPA data - use actual backend data\n                    semester_cgpas: student.semester_marksheets || [],\n                    semester_marksheets: student.semester_marksheets || []\n                }));\n            setAllStudents(studentsData);\n            setAvailableYears(getAvailableYears(studentsData));\n            setDepartmentStats(getDepartmentStats(studentsData));\n            return studentsData;\n        } catch (err) {\n            console.error('Error fetching all students:', err);\n            throw err;\n        }\n    };\n    // Debounce search term\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"StudentManagement.useEffect.timer\": ()=>{\n                    setDebouncedSearchTerm(searchTerm);\n                }\n            }[\"StudentManagement.useEffect.timer\"], 300); // 300ms delay\n            return ({\n                \"StudentManagement.useEffect\": ()=>clearTimeout(timer)\n            })[\"StudentManagement.useEffect\"];\n        }\n    }[\"StudentManagement.useEffect\"], [\n        searchTerm\n    ]);\n    // Fetch students from Django backend with pagination\n    const fetchStudents = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1;\n        try {\n            setLoading(true);\n            setError(null);\n            setIsRetrying(false);\n            const token = (0,_utils_auth__WEBPACK_IMPORTED_MODULE_3__.getAuthToken)();\n            if (!token) {\n                throw new Error('No authentication token found. Please login first.');\n            }\n            // Use allStudents if already loaded, otherwise fetch all\n            let allData = allStudents;\n            if (allStudents.length === 0) {\n                allData = await fetchAllStudents();\n            }\n            // Apply filters to get the filtered dataset\n            let filteredData = allData;\n            if (selectedDepartment) {\n                filteredData = filteredData.filter((student)=>student.department === selectedDepartment);\n            }\n            if (selectedYear !== 'all') {\n                filteredData = filteredData.filter((student)=>student.year === selectedYear);\n            }\n            if (debouncedSearchTerm) {\n                const searchLower = debouncedSearchTerm.toLowerCase();\n                filteredData = filteredData.filter((student)=>student.name.toLowerCase().includes(searchLower) || student.rollNumber.toLowerCase().includes(searchLower));\n            }\n            // Implement client-side pagination\n            const studentsPerPage = 10;\n            const startIndex = (page - 1) * studentsPerPage;\n            const endIndex = startIndex + studentsPerPage;\n            const paginatedStudents = filteredData.slice(startIndex, endIndex);\n            setStudents(paginatedStudents);\n            setCurrentPage(page);\n            setTotalPages(Math.ceil(filteredData.length / studentsPerPage));\n            setTotalStudents(filteredData.length);\n            setLoading(false);\n        } catch (err) {\n            var _err_response, _err_response1;\n            console.error('Error fetching students:', err);\n            if (((_err_response = err.response) === null || _err_response === void 0 ? void 0 : _err_response.status) === 401) {\n                setError('Authentication failed. Please login again.');\n            } else if (((_err_response1 = err.response) === null || _err_response1 === void 0 ? void 0 : _err_response1.status) === 403) {\n                setError('You do not have permission to view students. Admin access required.');\n            } else if (err.message.includes('token')) {\n                setError('Please login to access student management.');\n            } else {\n                setError(\"Error: \".concat(err.message));\n            }\n            setStudents([]);\n            setLoading(false);\n        }\n    };\n    // Helper function to determine year from branch (you can customize this logic)\n    const getYearFromBranch = (branch, student)=>{\n        if (student && student.joining_year && student.passout_year) {\n            return \"\".concat(student.joining_year, \"-\").concat(student.passout_year);\n        }\n        return 'N/A';\n    };\n    // Initial data fetch\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            fetchStudents();\n        }\n    }[\"StudentManagement.useEffect\"], []);\n    // Add this useEffect after your existing useEffect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            // Check if user is authenticated\n            const token = (0,_utils_auth__WEBPACK_IMPORTED_MODULE_3__.getAuthToken)();\n            if (!token) {\n                // Redirect to login page or show login prompt\n                setError('Please login to access student management.');\n                setLoading(false);\n                return;\n            }\n            fetchStudents();\n        }\n    }[\"StudentManagement.useEffect\"], []);\n    // Helper function to extract year from student ID (assuming format like CS2021001)\n    const getYearFromStudentId = (studentId)=>{\n        if (studentId && studentId.length >= 6) {\n            const yearPart = studentId.substring(2, 6);\n            if (!isNaN(yearPart)) {\n                return \"\".concat(4 - (new Date().getFullYear() - parseInt(yearPart)), \"th Year\");\n            }\n        }\n        return 'Unknown';\n    };\n    // Close dropdown when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"StudentManagement.useEffect.handleClickOutside\": (event)=>{\n                    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                        setIsYearDropdownOpen(false);\n                    }\n                }\n            }[\"StudentManagement.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"StudentManagement.useEffect\": ()=>{\n                    document.removeEventListener('mousedown', handleClickOutside);\n                }\n            })[\"StudentManagement.useEffect\"];\n        }\n    }[\"StudentManagement.useEffect\"], []);\n    // Get available years from students data\n    const getAvailableYears = (studentsData)=>{\n        const years = [\n            ...new Set(studentsData.map((student)=>student.year).filter((year)=>year && year !== 'N/A'))\n        ];\n        return years.sort();\n    };\n    // Get department statistics\n    const getDepartmentStats = (studentsData)=>{\n        const stats = {};\n        studentsData.forEach((student)=>{\n            if (student.department && student.department !== 'N/A') {\n                stats[student.department] = (stats[student.department] || 0) + 1;\n            }\n        });\n        return Object.entries(stats).map((param)=>{\n            let [department, count] = param;\n            return {\n                department,\n                count\n            };\n        });\n    };\n    // Get available passout years for selected department\n    const getAvailablePassoutYears = ()=>{\n        if (!selectedDepartment) return [];\n        const years = allStudents.filter((s)=>s.department === selectedDepartment && s.year && s.year !== 'N/A').map((s)=>{\n            // Extract passout year from year string (format: \"joining-passout\")\n            const parts = s.year.split('-');\n            return parts.length === 2 ? parts[1] : null;\n        }).filter((y)=>y).map(Number).filter((y)=>!isNaN(y));\n        // Unique and descending\n        return Array.from(new Set(years)).sort((a, b)=>b - a);\n    };\n    // Filter students for selected department and passout year\n    const getFilteredStudents = ()=>{\n        let filtered = allStudents;\n        if (selectedDepartment) {\n            filtered = filtered.filter((s)=>s.department === selectedDepartment);\n        }\n        if (selectedPassoutYear) {\n            filtered = filtered.filter((s)=>{\n                if (!s.year || s.year === 'N/A') return false;\n                const parts = s.year.split('-');\n                return parts.length === 2 && parts[1] === String(selectedPassoutYear);\n            });\n        }\n        if (debouncedSearchTerm) {\n            const searchLower = debouncedSearchTerm.toLowerCase();\n            filtered = filtered.filter((student)=>student.name.toLowerCase().includes(searchLower) || student.rollNumber.toLowerCase().includes(searchLower));\n        }\n        // CGPA filter\n        filtered = filtered.filter((student)=>{\n            const cgpa = parseFloat(student.cgpa);\n            if (cgpaMin && (isNaN(cgpa) || cgpa < parseFloat(cgpaMin))) return false;\n            if (cgpaMax && (isNaN(cgpa) || cgpa > parseFloat(cgpaMax))) return false;\n            return true;\n        });\n        return filtered;\n    };\n    // Update filters and refetch when dependencies change (but not searchTerm)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            if (allStudents.length > 0) {\n                fetchStudents(1); // Reset to page 1 when filters change\n            }\n        }\n    }[\"StudentManagement.useEffect\"], [\n        selectedDepartment,\n        selectedYear,\n        debouncedSearchTerm,\n        selectedPassoutYear\n    ]);\n    // Filter students based on selected department, year, and search term\n    const filteredStudents = students; // Students are already filtered in fetchStudents\n    const handleStudentClick = (student)=>{\n        setSelectedStudent(student);\n        setEditedStudent({\n            ...student\n        });\n        setIsEditing(false);\n    };\n    const handleBackToList = ()=>{\n        setSelectedStudent(null);\n        setIsEditing(false);\n        setEditedStudent(null);\n    };\n    const handleBackToDepartments = ()=>{\n        setSelectedDepartment(null);\n        setSelectedYear('all');\n        setSearchTerm('');\n    };\n    const handleEdit = ()=>{\n        setIsEditing(true);\n    };\n    const handleSave = async ()=>{\n        try {\n            setLoading(true);\n            // Helper function to clean data\n            const cleanValue = (value)=>{\n                if (value === '' || value === null || value === undefined) {\n                    return null;\n                }\n                // Handle string values\n                if (typeof value === 'string') {\n                    const trimmed = value.trim();\n                    return trimmed === '' ? null : trimmed;\n                }\n                return value;\n            };\n            // Helper function to clean numeric values\n            const cleanNumericValue = (value)=>{\n                if (value === '' || value === null || value === undefined) {\n                    return null;\n                }\n                if (typeof value === 'string') {\n                    const trimmed = value.trim();\n                    if (trimmed === '') return null;\n                    const parsed = parseInt(trimmed);\n                    return isNaN(parsed) ? null : parsed;\n                }\n                if (typeof value === 'number') {\n                    return isNaN(value) ? null : value;\n                }\n                return null;\n            };\n            // Helper function to clean string values specifically\n            const cleanStringValue = (value)=>{\n                if (value === '' || value === null || value === undefined) {\n                    return '';\n                }\n                return typeof value === 'string' ? value.trim() : String(value).trim();\n            };\n            // Split the name properly\n            const nameParts = editedStudent.name ? editedStudent.name.trim().split(' ') : [];\n            const firstName = nameParts[0] || '';\n            const lastName = nameParts.slice(1).join(' ') || '';\n            // Prepare the data for backend update\n            const updateData = {\n                // Basic information - ensure strings are not empty\n                first_name: cleanStringValue(firstName),\n                last_name: cleanStringValue(lastName),\n                student_id: cleanStringValue(editedStudent.rollNumber),\n                contact_email: cleanValue(editedStudent.email),\n                phone: cleanStringValue(editedStudent.phone),\n                branch: cleanStringValue(editedStudent.department),\n                gpa: cleanStringValue(editedStudent.gpa),\n                // Academic details - these should be integers\n                joining_year: cleanNumericValue(editedStudent.joining_year),\n                passout_year: cleanNumericValue(editedStudent.passout_year),\n                // Personal details\n                date_of_birth: cleanValue(editedStudent.dateOfBirth),\n                address: cleanStringValue(editedStudent.address),\n                city: cleanStringValue(editedStudent.city),\n                district: cleanStringValue(editedStudent.district),\n                state: cleanStringValue(editedStudent.state),\n                pincode: cleanStringValue(editedStudent.pincode),\n                country: cleanStringValue(editedStudent.country),\n                parent_contact: cleanStringValue(editedStudent.parentContact),\n                education: cleanStringValue(editedStudent.education),\n                skills: Array.isArray(editedStudent.skills) ? editedStudent.skills.filter((skill)=>skill && skill.trim()).join(', ') : cleanStringValue(editedStudent.skills),\n                // Academic scores - all as strings to match model\n                tenth_cgpa: cleanStringValue(editedStudent.tenth_cgpa),\n                tenth_percentage: cleanStringValue(editedStudent.tenth_percentage),\n                tenth_board: cleanStringValue(editedStudent.tenth_board),\n                tenth_school: cleanStringValue(editedStudent.tenth_school),\n                tenth_year_of_passing: cleanStringValue(editedStudent.tenth_year_of_passing),\n                tenth_location: cleanStringValue(editedStudent.tenth_location),\n                tenth_specialization: cleanStringValue(editedStudent.tenth_specialization),\n                twelfth_cgpa: cleanStringValue(editedStudent.twelfth_cgpa),\n                twelfth_percentage: cleanStringValue(editedStudent.twelfth_percentage),\n                twelfth_board: cleanStringValue(editedStudent.twelfth_board),\n                twelfth_school: cleanStringValue(editedStudent.twelfth_school),\n                twelfth_year_of_passing: cleanStringValue(editedStudent.twelfth_year_of_passing),\n                twelfth_location: cleanStringValue(editedStudent.twelfth_location),\n                twelfth_specialization: cleanStringValue(editedStudent.twelfth_specialization)\n            };\n            // Add semester CGPAs if they exist\n            if (editedStudent.semester_cgpas && Array.isArray(editedStudent.semester_cgpas)) {\n                editedStudent.semester_cgpas.forEach((semesterData)=>{\n                    if (semesterData.semester >= 1 && semesterData.semester <= 8 && semesterData.cgpa) {\n                        updateData[\"semester\".concat(semesterData.semester, \"_cgpa\")] = cleanStringValue(semesterData.cgpa);\n                    }\n                });\n            }\n            // Remove empty string values but keep nulls for proper field clearing\n            const cleanedUpdateData = Object.fromEntries(Object.entries(updateData).filter((param)=>{\n                let [key, value] = param;\n                // Keep nulls for clearing fields, remove empty strings except for required fields\n                const requiredFields = [\n                    'first_name',\n                    'last_name',\n                    'student_id',\n                    'gpa'\n                ];\n                if (requiredFields.includes(key)) {\n                    return value !== null && value !== undefined;\n                }\n                return value !== null && value !== undefined && value !== '';\n            }));\n            // Ensure required fields have default values if missing\n            if (!cleanedUpdateData.first_name) cleanedUpdateData.first_name = 'Student';\n            if (!cleanedUpdateData.last_name) cleanedUpdateData.last_name = '';\n            if (!cleanedUpdateData.student_id) cleanedUpdateData.student_id = \"TEMP_\".concat(Date.now());\n            if (!cleanedUpdateData.gpa) cleanedUpdateData.gpa = '0.0';\n            // Debug logging\n            console.log('Original editedStudent:', editedStudent);\n            console.log('Update data being sent:', cleanedUpdateData);\n            console.log('Student ID:', editedStudent.id);\n            // Make API call to update student\n            const updatedStudent = await _api_optimized__WEBPACK_IMPORTED_MODULE_2__.studentsAPI.updateStudent(editedStudent.id, cleanedUpdateData);\n            // Update the student in the list with the response data\n            const updatedStudentData = {\n                ...editedStudent,\n                ...updatedStudent,\n                name: \"\".concat(updatedStudent.first_name || '', \" \").concat(updatedStudent.last_name || '').trim(),\n                rollNumber: updatedStudent.student_id,\n                email: updatedStudent.contact_email,\n                department: updatedStudent.branch,\n                gpa: updatedStudent.gpa\n            };\n            setStudents((prev)=>prev.map((student)=>student.id === editedStudent.id ? updatedStudentData : student));\n            setSelectedStudent(updatedStudentData);\n            setIsEditing(false);\n            // Show success message\n            alert('Student profile updated successfully!');\n        } catch (error) {\n            var _error_response;\n            console.error('Error updating student:', error);\n            // More detailed error logging\n            if (error.response) {\n                console.error('Error response status:', error.response.status);\n                console.error('Error response data:', error.response.data);\n                console.error('Error response headers:', error.response.headers);\n            }\n            let errorMessage = 'Failed to update student profile. Please try again.';\n            if ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) {\n                // Handle validation errors\n                if (typeof error.response.data === 'object') {\n                    const errorDetails = [];\n                    for (const [field, messages] of Object.entries(error.response.data)){\n                        if (Array.isArray(messages)) {\n                            errorDetails.push(\"\".concat(field, \": \").concat(messages.join(', ')));\n                        } else {\n                            errorDetails.push(\"\".concat(field, \": \").concat(messages));\n                        }\n                    }\n                    if (errorDetails.length > 0) {\n                        errorMessage = \"Validation errors:\\n\".concat(errorDetails.join('\\n'));\n                    }\n                } else if (error.response.data.detail) {\n                    errorMessage = error.response.data.detail;\n                } else if (error.response.data.message) {\n                    errorMessage = error.response.data.message;\n                }\n            }\n            alert(\"Update failed: \".concat(errorMessage));\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleCancel = ()=>{\n        setEditedStudent({\n            ...selectedStudent\n        });\n        setIsEditing(false);\n    };\n    const handleInputChange = (field, value)=>{\n        setEditedStudent((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    // Handle retry button click\n    const handleRetry = ()=>{\n        setIsRetrying(true);\n        fetchStudents();\n    };\n    // Help developers find the correct API endpoint\n    const debugBackend = ()=>{\n        window.open('http://localhost:8000/admin/');\n    };\n    const handleSearch = ()=>{\n        // Force immediate search without waiting for debounce\n        setDebouncedSearchTerm(searchTerm);\n        setCurrentPage(1);\n    };\n    // Handle pagination\n    const handlePageChange = (newPage)=>{\n        if (newPage >= 1 && newPage <= totalPages) {\n            fetchStudents(newPage);\n        }\n    };\n    // Handle search input change\n    const handleSearchInputChange = (e)=>{\n        setSearchTerm(e.target.value);\n    // Don't trigger immediate search, let debounce handle it\n    };\n    // Handle search input key press\n    const handleSearchKeyDown = (e)=>{\n        if (e.key === 'Enter') {\n            e.preventDefault();\n            handleSearch();\n        }\n    };\n    if (loading) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 p-6 ml-20 overflow-y-auto h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center h-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mb-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                    lineNumber: 626,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-gray-600\",\n                    children: \"Loading students...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                    lineNumber: 627,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n            lineNumber: 625,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n        lineNumber: 624,\n        columnNumber: 5\n    }, this);\n    if (error && students.length === 0) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 p-6 ml-20 overflow-y-auto h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center h-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-red-500 mb-4 text-center max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-semibold text-lg mb-2\",\n                            children: \"Access Error\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 636,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 637,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 text-sm text-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Possible solutions:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                    lineNumber: 639,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"list-disc list-inside mt-2 text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Make sure you're logged in with admin credentials\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                            lineNumber: 641,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Check if your session has expired\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                            lineNumber: 642,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Verify Django server is running on port 8000\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                            lineNumber: 643,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Ensure proper permissions are set in Django\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                            lineNumber: 644,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                    lineNumber: 640,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 638,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                    lineNumber: 635,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-3 mt-4\",\n                    children: [\n                        !error.includes('login') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleRetry,\n                            className: \"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                            disabled: isRetrying,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_GraduationCap_RefreshCw_Save_Search_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-4 h-4 \".concat(isRetrying ? 'animate-spin' : '')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                    lineNumber: 655,\n                                    columnNumber: 15\n                                }, this),\n                                isRetrying ? 'Retrying...' : 'Retry'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 650,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>window.location.href = '/login',\n                            className: \"flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_GraduationCap_RefreshCw_Save_Search_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                    lineNumber: 663,\n                                    columnNumber: 13\n                                }, this),\n                                \"Go to Login\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 659,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                    lineNumber: 648,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n            lineNumber: 634,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n        lineNumber: 633,\n        columnNumber: 5\n    }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 p-6 ml-20 overflow-y-auto h-full\",\n        children: !selectedStudent ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: !selectedDepartment ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DepartmentCards__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                departmentOptions: departmentOptions,\n                departmentStats: departmentStats,\n                allStudents: allStudents,\n                onSelect: setSelectedDepartment\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                lineNumber: 676,\n                columnNumber: 13\n            }, this) : !selectedPassoutYear ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PassoutYearCards__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                departmentLabel: (_departmentOptions_find = departmentOptions.find((d)=>d.value === selectedDepartment)) === null || _departmentOptions_find === void 0 ? void 0 : _departmentOptions_find.label,\n                onBack: handleBackToDepartments,\n                getAvailablePassoutYears: getAvailablePassoutYears,\n                allStudents: allStudents,\n                selectedDepartment: selectedDepartment,\n                onSelectYear: setSelectedPassoutYear\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                lineNumber: 683,\n                columnNumber: 13\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StudentList__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                departmentLabel: (_departmentOptions_find1 = departmentOptions.find((d)=>d.value === selectedDepartment)) === null || _departmentOptions_find1 === void 0 ? void 0 : _departmentOptions_find1.label,\n                passoutYear: selectedPassoutYear,\n                onBack: ()=>setSelectedPassoutYear(null),\n                searchTerm: searchTerm,\n                handleSearchInputChange: handleSearchInputChange,\n                handleSearchKeyDown: handleSearchKeyDown,\n                cgpaMin: cgpaMin,\n                setCgpaMin: setCgpaMin,\n                cgpaMax: cgpaMax,\n                setCgpaMax: setCgpaMax,\n                handleSearch: handleSearch,\n                getFilteredStudents: getFilteredStudents,\n                currentPage: currentPage,\n                handlePageChange: handlePageChange,\n                handleStudentClick: handleStudentClick,\n                loading: loading\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                lineNumber: 692,\n                columnNumber: 13\n            }, this)\n        }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StudentProfile__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            selectedStudent: selectedStudent,\n            editedStudent: editedStudent,\n            isEditing: isEditing,\n            handleBackToList: handleBackToList,\n            handleEdit: handleEdit,\n            handleSave: handleSave,\n            handleCancel: handleCancel,\n            handleInputChange: handleInputChange,\n            departmentOptions: departmentOptions\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n            lineNumber: 713,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n        lineNumber: 672,\n        columnNumber: 5\n    }, this);\n}\n_s(StudentManagement, \"84yS5p8/FAIhW7pAdcz1mPZ+49Q=\");\n_c = StudentManagement;\nvar _c;\n$RefreshReg$(_c, \"StudentManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/student-management/page.jsx\n"));

/***/ })

});