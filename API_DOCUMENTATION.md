# Optimized API Documentation

This document provides comprehensive documentation for the optimized API endpoints that implement server-side pagination and efficient data loading.

## Table of Contents

1. [Authentication](#authentication)
2. [Response Format](#response-format)
3. [Pagination](#pagination)
4. [Student Management API](#student-management-api)
5. [Company Management API](#company-management-api)
6. [Metrics API](#metrics-api)
7. [Error Handling](#error-handling)
8. [Performance Guidelines](#performance-guidelines)

## Authentication

All API endpoints require authentication. Include the JWT token in the Authorization header:

```http
Authorization: Bearer <your-jwt-token>
```

## Response Format

All API responses follow a consistent format:

### Success Response
```json
{
  "success": true,
  "message": "Data retrieved successfully",
  "data": [...],
  "pagination": {
    "page": 1,
    "page_size": 20,
    "total_count": 150,
    "total_pages": 8,
    "has_next": true,
    "has_previous": false
  },
  "metadata": {
    "available_departments": ["Computer Science", "Electronics"],
    "available_years": [2023, 2024, 2025]
  }
}
```

### Error Response
```json
{
  "success": false,
  "message": "Error message",
  "errors": {
    "field_name": ["Specific error details"]
  },
  "error_code": "VALIDATION_ERROR"
}
```

## Pagination

All list endpoints support pagination with the following parameters:

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `page` | integer | 1 | Page number (1-based) |
| `page_size` | integer | 20 | Items per page (max: 100) |

### Pagination Response
```json
{
  "pagination": {
    "page": 1,
    "page_size": 20,
    "total_count": 150,
    "total_pages": 8,
    "has_next": true,
    "has_previous": false,
    "start_index": 1,
    "end_index": 20
  }
}
```

## Student Management API

### Get Students (Optimized)

**Endpoint:** `GET /api/v1/accounts/students/optimized/`

**Description:** Retrieve students with server-side pagination and filtering.

**Parameters:**

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `page` | integer | Page number | `1` |
| `page_size` | integer | Items per page | `20` |
| `search` | string | Search in name, ID, email | `"john"` |
| `department` | string | Filter by department | `"Computer Science"` |
| `branch` | string | Filter by branch | `"CSE"` |
| `passout_year` | integer | Filter by graduation year | `2024` |
| `joining_year` | integer | Filter by joining year | `2020` |
| `year_range` | string | Filter by year range | `"2020-2024"` |
| `cgpa_min` | float | Minimum CGPA | `7.0` |
| `cgpa_max` | float | Maximum CGPA | `9.0` |

**Example Request:**
```http
GET /api/v1/accounts/students/optimized/?page=1&page_size=20&department=Computer%20Science&cgpa_min=7.0
```

**Example Response:**
```json
{
  "success": true,
  "message": "Data retrieved successfully",
  "data": [
    {
      "id": 1,
      "first_name": "John",
      "last_name": "Doe",
      "student_id": "CS2020001",
      "branch": "Computer Science",
      "passout_year": 2024,
      "joining_year": 2020,
      "gpa": 8.5,
      "contact_email": "<EMAIL>",
      "user": {
        "email": "<EMAIL>"
      }
    }
  ],
  "pagination": {
    "page": 1,
    "page_size": 20,
    "total_count": 45,
    "total_pages": 3,
    "has_next": true,
    "has_previous": false
  },
  "metadata": {
    "available_departments": ["Computer Science", "Electronics", "Mechanical"],
    "available_years": [2023, 2024, 2025],
    "total_students": 1250
  }
}
```

### Search Students

**Endpoint:** `GET /api/v1/accounts/students/optimized/`

**Description:** Search students with advanced filtering.

**Example Request:**
```http
GET /api/v1/accounts/students/optimized/?search=john&department=Computer%20Science&cgpa_min=8.0
```

## Company Management API

### Get Companies (Optimized)

**Endpoint:** `GET /api/v1/companies/optimized/`

**Description:** Retrieve companies with server-side pagination and filtering.

**Parameters:**

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `page` | integer | Page number | `1` |
| `page_size` | integer | Items per page | `20` |
| `search` | string | Search in name, description, industry | `"tech"` |
| `tier` | string | Filter by tier | `"Tier 1"` |
| `industry` | string | Filter by industry | `"Technology"` |
| `location` | string | Filter by location | `"Bangalore"` |
| `size` | string | Filter by company size | `"1000+"` |
| `campus_recruiting` | boolean | Filter by campus recruiting | `true` |
| `min_jobs` | integer | Minimum active jobs | `5` |
| `founded_after` | integer | Founded after year | `2000` |

**Example Request:**
```http
GET /api/v1/companies/optimized/?page=1&tier=Tier%201&campus_recruiting=true
```

**Example Response:**
```json
{
  "success": true,
  "message": "Data retrieved successfully",
  "data": [
    {
      "id": 1,
      "name": "Google",
      "tier": "Tier 1",
      "industry": "Technology",
      "location": "Mountain View, CA",
      "size": "100,000+ employees",
      "campus_recruiting": true,
      "total_active_jobs": 15,
      "total_applicants": 1250,
      "founded": "1998",
      "website": "https://google.com"
    }
  ],
  "pagination": {
    "page": 1,
    "page_size": 20,
    "total_count": 25,
    "total_pages": 2,
    "has_next": true,
    "has_previous": false
  },
  "metadata": {
    "available_industries": ["Technology", "Finance", "Healthcare"],
    "available_locations": ["Bangalore", "Mumbai", "Delhi"],
    "tier_distribution": [
      {"tier": "Tier 1", "count": 25},
      {"tier": "Tier 2", "count": 45},
      {"tier": "Tier 3", "count": 80}
    ],
    "total_companies": 150,
    "campus_recruiting_count": 75
  }
}
```

## Metrics API

### Get Cached Metrics

**Endpoint:** `GET /api/v1/metrics/cached/`

**Description:** Retrieve cached metrics for dashboard and analytics.

**Parameters:**

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `type` | string | Metric type | `"dashboard_stats"` |
| `refresh` | boolean | Force refresh cache | `false` |

**Available Metric Types:**
- `dashboard_stats` - Overall dashboard statistics
- `student_stats` - Student-related metrics
- `company_stats` - Company-related metrics
- `job_stats` - Job posting metrics
- `application_stats` - Application metrics
- `department_stats` - Department-wise statistics
- `placement_stats` - Placement statistics

**Example Request:**
```http
GET /api/v1/metrics/cached/?type=dashboard_stats
```

**Example Response:**
```json
{
  "success": true,
  "data": {
    "total_jobs": 150,
    "total_applications": 2500,
    "total_students": 1250,
    "total_companies": 75,
    "active_jobs": 120,
    "pending_applications": 450,
    "hiring_companies": 45,
    "placement_rate": 78.5,
    "last_updated": "2024-01-15T10:30:00Z"
  }
}
```

### Get Department Statistics

**Example Request:**
```http
GET /api/v1/metrics/cached/?type=department_stats
```

**Example Response:**
```json
{
  "success": true,
  "data": {
    "departments": [
      {
        "branch": "Computer Science",
        "total_students": 450,
        "current_year_students": 120,
        "avg_gpa": 8.2,
        "with_applications": 380,
        "placed_students": 95
      }
    ],
    "total_departments": 8,
    "last_updated": "2024-01-15T10:30:00Z"
  }
}
```

### Get Placement Statistics

**Example Request:**
```http
GET /api/v1/metrics/cached/?type=placement_stats
```

**Example Response:**
```json
{
  "success": true,
  "data": {
    "total_eligible": 120,
    "total_placed": 95,
    "placement_rate": 79.17,
    "company_wise_placements": [
      {
        "job__company__name": "Google",
        "job__company__tier": "Tier 1",
        "count": 15
      }
    ],
    "department_wise_placements": [
      {
        "branch": "Computer Science",
        "total_students": 120,
        "placed_students": 95,
        "placement_rate": 79.17
      }
    ],
    "last_updated": "2024-01-15T10:30:00Z"
  }
}
```

## Error Handling

### Common Error Codes

| Code | Description |
|------|-------------|
| `VALIDATION_ERROR` | Request validation failed |
| `AUTHENTICATION_ERROR` | Authentication required or failed |
| `PERMISSION_ERROR` | Insufficient permissions |
| `NOT_FOUND` | Resource not found |
| `RATE_LIMIT_ERROR` | Rate limit exceeded |
| `SERVER_ERROR` | Internal server error |

### Example Error Response
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": {
    "page_size": ["Ensure this value is less than or equal to 100."],
    "cgpa_min": ["Enter a valid number."]
  },
  "error_code": "VALIDATION_ERROR"
}
```

## Performance Guidelines

### Best Practices

1. **Use Pagination**: Always use pagination for large datasets
   ```javascript
   // Good
   const response = await studentsAPI.getStudents({ page: 1, page_size: 20 });
   
   // Bad - Don't load all data at once
   const allStudents = await fetchAllStudents();
   ```

2. **Implement Debouncing**: Use debouncing for search functionality
   ```javascript
   const debouncedSearch = debounce((searchTerm) => {
     fetchStudents({ search: searchTerm });
   }, 300);
   ```

3. **Cache Metrics**: Use cached metrics instead of real-time calculations
   ```javascript
   // Good
   const stats = await dashboardAPI.getDashboardStats();
   
   // Bad - Don't calculate in real-time
   const stats = calculateStatsRealTime();
   ```

4. **Use Appropriate Page Sizes**: 
   - Small screens: 10-20 items
   - Desktop: 20-50 items
   - Maximum: 100 items

5. **Filter Early**: Apply filters on the server side
   ```javascript
   // Good
   const students = await studentsAPI.getStudents({
     department: 'Computer Science',
     cgpa_min: 7.0
   });
   
   // Bad - Don't filter on client side
   const allStudents = await studentsAPI.getStudents();
   const filtered = allStudents.filter(s => s.department === 'Computer Science');
   ```

### Performance Monitoring

Monitor these metrics:
- Response time for paginated endpoints
- Cache hit rates for metrics
- Database query count per request
- Memory usage on frontend
- Network payload sizes

### Optimization Commands

Run these commands regularly for optimal performance:

```bash
# Warm up caches
python manage.py warm_cache

# Check cache status
python manage.py cache_status

# Optimize database
python manage.py optimize_database --all

# Initialize metrics
python manage.py init_metrics
```
